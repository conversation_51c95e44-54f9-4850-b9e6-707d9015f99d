#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量化批次处理器
基于pandas groupby和numpy向量化操作的高性能股票数据处理

主要优化：
1. 使用groupby + transform进行批量技术指标计算
2. 向量化特征工程，避免逐股票处理
3. 减少数据复制和内存分配
4. 利用pandas和numpy的C级别优化
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Any
import time
from concurrent.futures import ProcessPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class VectorizedBatchProcessor:
    """向量化批次处理器"""
    
    def __init__(self):
        self.feature_columns = [
            'ma3', 'ma5', 'ma10', 'ma20', 'ma60',
            'volume_ma3', 'volume_ma5', 'volume_ma10',
            'boll_upper', 'boll_middle', 'boll_lower', 'boll_bandwidth', 'boll_pct_b',
            'sar', 'sar_direction', 'emv', 'emv_ma14',
            'cr', 'cr_ma1', 'cr_ma2', 'cr_ma3',
            'price_trend_short', 'price_trend_mid', 'price_trend_long', 'trend_strength',
            'turnover_rate_ma5', 'turnover_rate_ma10', 'turnover_rate_ma20',
            'turnover_rate_change', 'turnover_rate_acc', 'turnover_rate_rank_20d',
            'unusual_volume', 'turnover_oscillator'
        ]
    
    def vectorized_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        向量化计算技术指标
        使用groupby + transform进行批量计算，避免逐股票处理
        """
        logging.info(f"开始向量化计算技术指标，数据量: {len(df)} 行，{df['ts_code'].nunique()} 只股票")
        start_time = time.time()
        
        # 确保数据按股票代码和日期排序
        df = df.sort_values(['ts_code', 'trade_date']).reset_index(drop=True)
        
        # 1. 向量化计算移动平均线
        logging.info("计算移动平均线...")
        for period in [3, 5, 10, 20, 60]:
            df[f'ma{period}'] = df.groupby('ts_code')['close'].transform(
                lambda x: x.rolling(window=period, min_periods=1).mean()
            )
        
        # 2. 向量化计算成交量移动平均
        logging.info("计算成交量移动平均...")
        for vol_period in [3, 5, 10]:
            df[f'volume_ma{vol_period}'] = df.groupby('ts_code')['vol'].transform(
                lambda x: x.rolling(window=vol_period, min_periods=1).mean()
            )
        
        # 3. 向量化计算布林带
        logging.info("计算布林带指标...")
        def calculate_bollinger_bands(group):
            close = group['close'].values
            if len(close) < 20:
                # 数据不足时使用简化计算
                upper = close * 1.02
                middle = close
                lower = close * 0.98
            else:
                try:
                    # 使用pandas实现布林带计算
                    close_series = pd.Series(close)
                    middle = close_series.rolling(window=20, min_periods=1).mean().values
                    std = close_series.rolling(window=20, min_periods=1).std().values
                    upper = middle + (std * 2)
                    lower = middle - (std * 2)

                    # 处理NaN值
                    upper = pd.Series(upper).fillna(method='bfill').fillna(close * 1.02).values
                    middle = pd.Series(middle).fillna(method='bfill').fillna(close).values
                    lower = pd.Series(lower).fillna(method='bfill').fillna(close * 0.98).values
                except:
                    upper = close * 1.02
                    middle = close
                    lower = close * 0.98

            result = pd.DataFrame({
                'boll_upper': upper,
                'boll_middle': middle,
                'boll_lower': lower
            }, index=group.index)

            # 计算布林带宽度和百分比B
            result['boll_bandwidth'] = (result['boll_upper'] - result['boll_lower']) / (result['boll_middle'] + 1e-6) * 100
            result['boll_pct_b'] = (close - result['boll_lower']) / (result['boll_upper'] - result['boll_lower'] + 1e-6)

            return result
        
        boll_results = df.groupby('ts_code').apply(calculate_bollinger_bands)
        boll_results.index = boll_results.index.droplevel(0)  # 移除多级索引
        df = df.join(boll_results)
        
        # 4. 向量化计算SAR指标（简化版本）
        logging.info("计算SAR指标...")
        def calculate_sar(group):
            high = group['high'].values
            low = group['low'].values
            close = group['close'].values

            if len(high) < 2:
                sar = close
                sar_direction = np.ones(len(close))
            else:
                try:
                    # 简化的SAR计算（使用移动平均作为近似）
                    sar = pd.Series(close).rolling(window=5, min_periods=1).mean().values
                    sar_direction = (close > sar).astype(int)
                except:
                    sar = close
                    sar_direction = np.ones(len(close))

            return pd.DataFrame({
                'sar': sar,
                'sar_direction': sar_direction
            }, index=group.index)
        
        sar_results = df.groupby('ts_code').apply(calculate_sar)
        sar_results.index = sar_results.index.droplevel(0)
        df = df.join(sar_results)
        
        # 5. 向量化计算EMV指标
        logging.info("计算EMV指标...")
        def calculate_emv(group):
            high = group['high'].values
            low = group['low'].values
            volume = group['vol'].values
            
            if len(high) < 2:
                emv = np.zeros(len(high))
                emv_ma14 = np.zeros(len(high))
            else:
                try:
                    # 计算EMV
                    high_low = high - low
                    vol_div = volume / 1000000
                    emv = (high + low) / 2 - (np.roll(high, 1) + np.roll(low, 1)) / 2
                    emv = emv * high_low / (vol_div + 1e-6)
                    emv[0] = 0  # 第一个元素设为0
                    
                    # 计算EMV的14日移动平均
                    emv_ma14 = pd.Series(emv).rolling(window=14, min_periods=1).mean().values
                except:
                    emv = np.zeros(len(high))
                    emv_ma14 = np.zeros(len(high))
            
            return pd.DataFrame({
                'emv': emv,
                'emv_ma14': emv_ma14
            }, index=group.index)
        
        emv_results = df.groupby('ts_code').apply(calculate_emv)
        emv_results.index = emv_results.index.droplevel(0)
        df = df.join(emv_results)
        
        # 6. 向量化计算CR指标
        logging.info("计算CR指标...")
        def calculate_cr_vectorized(group):
            high = group['high'].values
            low = group['low'].values
            close = group['close'].values
            open_price = group['open'].values
            
            if len(close) < 2:
                cr = np.full(len(close), 100.0)
                cr_ma1 = cr.copy()
                cr_ma2 = cr.copy()
                cr_ma3 = cr.copy()
            else:
                try:
                    # 计算中间价格
                    mid_price = (high + low + close + open_price) / 4
                    mid_price_prev = np.roll(mid_price, 1)
                    mid_price_prev[0] = mid_price[0]
                    
                    # 计算强弱指标
                    p1 = high - mid_price_prev
                    p2 = mid_price_prev - low
                    p1[0] = 0
                    p2[0] = 0
                    
                    # 计算CR值
                    cr_period = min(26, len(close))
                    p1_sum = pd.Series(p1).rolling(window=cr_period, min_periods=1).sum().values
                    p2_sum = pd.Series(p2).rolling(window=cr_period, min_periods=1).sum().values
                    
                    # 防止除零
                    p2_sum = np.where(p2_sum == 0, 1e-6, p2_sum)
                    cr = np.clip(p1_sum / p2_sum * 100, 0, 1000)
                    
                    # 计算CR移动平均
                    cr_series = pd.Series(cr)
                    cr_ma1 = cr_series.rolling(window=min(5, len(cr)), min_periods=1).mean().values
                    cr_ma2 = cr_series.rolling(window=min(10, len(cr)), min_periods=1).mean().values
                    cr_ma3 = cr_series.rolling(window=min(20, len(cr)), min_periods=1).mean().values
                    
                except:
                    cr = np.full(len(close), 100.0)
                    cr_ma1 = cr.copy()
                    cr_ma2 = cr.copy()
                    cr_ma3 = cr.copy()
            
            return pd.DataFrame({
                'cr': cr,
                'cr_ma1': cr_ma1,
                'cr_ma2': cr_ma2,
                'cr_ma3': cr_ma3
            }, index=group.index)
        
        cr_results = df.groupby('ts_code').apply(calculate_cr_vectorized)
        cr_results.index = cr_results.index.droplevel(0)
        df = df.join(cr_results)
        
        # 7. 向量化计算趋势指标
        logging.info("计算趋势指标...")
        df['price_trend_short'] = (df['ma3'] > df['ma5']).astype(int)
        df['price_trend_mid'] = (df['ma5'] > df['ma10']).astype(int)
        df['price_trend_long'] = (df['ma10'] > df['ma20']).astype(int)
        df['trend_strength'] = df['price_trend_short'] + df['price_trend_mid'] + df['price_trend_long']
        
        # 8. 向量化计算换手率特征
        logging.info("计算换手率特征...")
        if 'turnover_rate' in df.columns:
            # 换手率移动平均
            for period in [5, 10, 20]:
                df[f'turnover_rate_ma{period}'] = df.groupby('ts_code')['turnover_rate'].transform(
                    lambda x: x.rolling(window=period, min_periods=1).mean()
                )
            
            # 换手率变动和加速度
            df['turnover_rate_change'] = df.groupby('ts_code')['turnover_rate'].transform(
                lambda x: x.pct_change() * 100
            )
            df['turnover_rate_acc'] = df.groupby('ts_code')['turnover_rate_change'].transform(
                lambda x: x.diff()
            )
            
            # 相对历史换手率水平
            df['turnover_rate_rank_20d'] = df.groupby('ts_code')['turnover_rate'].transform(
                lambda x: x.rolling(20, min_periods=1).rank(pct=True)
            )
            
            # 异常放量指标
            turnover_ma20 = df.groupby('ts_code')['turnover_rate'].transform(
                lambda x: x.rolling(20, min_periods=1).mean()
            )
            df['unusual_volume'] = (df['turnover_rate'] > turnover_ma20 * 2).astype(int)
            
            # 换手率震荡指标
            df['turnover_oscillator'] = (df['turnover_rate'] - df['turnover_rate_ma5']) / (df['turnover_rate_ma5'] + 1e-6)
        else:
            # 如果没有换手率数据，使用默认值
            for col in ['turnover_rate_ma5', 'turnover_rate_ma10', 'turnover_rate_ma20',
                       'turnover_rate_change', 'turnover_rate_acc', 'turnover_rate_rank_20d',
                       'unusual_volume', 'turnover_oscillator']:
                df[col] = 0
        
        # 填充NaN值
        for col in self.feature_columns:
            if col in df.columns:
                df[col] = df[col].fillna(0)
        
        elapsed_time = time.time() - start_time
        logging.info(f"✅ 向量化技术指标计算完成，耗时: {elapsed_time:.2f}秒")
        
        return df
    
    def process_stock_batch_vectorized(self, stock_codes: List[str], dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        向量化批次处理函数
        替代原来的逐股票处理方式
        """
        if dataframe.empty:
            return dataframe
        
        batch_size = len(stock_codes)
        logging.info(f"🚀 开始向量化批次处理: {batch_size}只股票")
        start_time = time.time()
        
        try:
            # 筛选当前批次的数据
            batch_df = dataframe[dataframe['ts_code'].isin(stock_codes)].copy()
            
            if batch_df.empty:
                logging.warning(f"批次数据为空，股票代码: {stock_codes}")
                return pd.DataFrame()
            
            # 向量化计算所有技术指标
            result_df = self.vectorized_technical_indicators(batch_df)
            
            elapsed_time = time.time() - start_time
            logging.info(f"✅ 向量化批次处理完成: {batch_size}只股票，耗时: {elapsed_time:.2f}秒，平均每只股票: {elapsed_time/batch_size:.3f}秒")
            
            return result_df
            
        except Exception as e:
            logging.error(f"❌ 向量化批次处理出错: {e}")
            return pd.DataFrame()


def compare_performance():
    """性能对比测试"""
    # 生成测试数据
    np.random.seed(42)
    n_stocks = 50
    n_days = 100
    
    test_data = []
    for i in range(n_stocks):
        stock_code = f"00{i:04d}.SZ"
        for j in range(n_days):
            base_price = 10 + np.random.randn() * 2
            test_data.append({
                'ts_code': stock_code,
                'trade_date': f"2024{j//30+1:02d}{j%30+1:02d}",
                'open': base_price + np.random.randn() * 0.1,
                'high': base_price + abs(np.random.randn() * 0.2),
                'low': base_price - abs(np.random.randn() * 0.2),
                'close': base_price + np.random.randn() * 0.1,
                'vol': np.random.randint(1000, 10000),
                'turnover_rate': np.random.uniform(0.1, 5.0)
            })
    
    df = pd.DataFrame(test_data)
    
    # 测试向量化处理
    processor = VectorizedBatchProcessor()
    stock_codes = df['ts_code'].unique().tolist()
    
    start_time = time.time()
    result = processor.process_stock_batch_vectorized(stock_codes, df)
    vectorized_time = time.time() - start_time
    
    print(f"向量化处理时间: {vectorized_time:.2f}秒")
    print(f"处理数据量: {len(df)} 行，{len(stock_codes)} 只股票")
    print(f"平均每只股票处理时间: {vectorized_time/len(stock_codes):.4f}秒")
    print(f"结果数据量: {len(result)} 行")


if __name__ == "__main__":
    compare_performance()
