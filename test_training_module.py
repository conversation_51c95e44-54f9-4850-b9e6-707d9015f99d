#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练模块修复测试脚本
专门测试修复的训练相关功能
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
import importlib.util
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'training_fixes_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

def test_time_variable_fix():
    """测试time变量修复"""
    logging.info("🔧 测试time变量修复...")
    
    try:
        # 模拟trial对象
        class MockTrial:
            def __init__(self):
                self.number = 1
        
        trial = MockTrial()
        
        # 测试修复后的代码逻辑（模拟第5777行的逻辑）
        import time as time_module
        trial_unique_id = f"{trial.number}_{int(time_module.time() * 1000) % 10000}"
        
        logging.info(f"✅ trial_unique_id生成成功: {trial_unique_id}")
        
        # 验证格式
        parts = trial_unique_id.split('_')
        if len(parts) == 2 and parts[0] == '1' and parts[1].isdigit():
            logging.info("✅ trial_unique_id格式正确")
            return True
        else:
            logging.error(f"❌ trial_unique_id格式错误: {trial_unique_id}")
            return False
            
    except Exception as e:
        logging.error(f"❌ time变量修复测试失败: {str(e)}")
        return False

def test_optimizer_definition():
    """测试optimizer定义修复"""
    logging.info("🔧 测试optimizer定义修复...")
    
    try:
        # 模拟Config类
        class MockConfig:
            USE_ADEMAMIX = False
            DEFAULT_LEARNING_RATE = 0.001
            GRADIENT_CLIP_NORM = 1.0
        
        # 测试optimizer定义逻辑
        learning_rate = MockConfig.DEFAULT_LEARNING_RATE
        
        # 测试Adam优化器创建
        import tensorflow as tf
        optimizer = tf.keras.optimizers.Adam(
            learning_rate=float(learning_rate),
            clipnorm=MockConfig.GRADIENT_CLIP_NORM,
            beta_1=0.9,
            beta_2=0.999,
            epsilon=1e-7
        )
        
        logging.info(f"✅ Adam优化器创建成功: {type(optimizer)}")
        
        # 验证optimizer在locals()中存在
        if 'optimizer' in locals():
            logging.info("✅ optimizer变量定义正确")
            return True
        else:
            logging.error("❌ optimizer变量未定义")
            return False
            
    except Exception as e:
        logging.error(f"❌ optimizer定义测试失败: {str(e)}")
        return False

def test_dynamic_metric_matching():
    """测试动态指标匹配修复"""
    logging.info("🔧 测试动态指标匹配修复...")
    
    try:
        # 模拟可用指标
        available_metrics = [
            'val_loss',
            'val_classification_output_1_auc_1',
            'val_classification_output_1_accuracy_1',
            'val_classification_output_2_auc_2',
            'val_binary_accuracy'
        ]
        
        # 创建模拟的_find_dynamic_auc_metric函数
        def _find_dynamic_auc_metric(available_metrics):
            """动态查找AUC指标，支持多轮训练的不同后缀"""
            import re
            
            logging.info(f"🔍 开始动态匹配指标，可用指标: {available_metrics}")
            
            # 定义AUC指标的正则表达式模式
            auc_patterns = [
                r'val_classification_output_1_auc.*',
                r'val_.*classification_output_1.*auc.*',
                r'val_auc.*',
                r'val_binary_accuracy.*'
            ]
            
            # 搜索匹配的AUC指标
            for pattern in auc_patterns:
                matching_keys = [k for k in available_metrics if re.search(pattern, k)]
                if matching_keys:
                    latest_key = sorted(matching_keys)[-1]
                    logging.info(f"✅ 动态匹配找到AUC指标: {latest_key} (模式: {pattern})")
                    return latest_key
            
            return None
        
        # 测试指标匹配
        result = _find_dynamic_auc_metric(available_metrics)
        
        if result == 'val_classification_output_1_auc_1':
            logging.info("✅ 动态指标匹配成功")
            return True
        else:
            logging.error(f"❌ 动态指标匹配失败，期望'val_classification_output_1_auc_1'，实际'{result}'")
            return False
            
    except Exception as e:
        logging.error(f"❌ 动态指标匹配测试失败: {str(e)}")
        return False

def test_dataset_creation():
    """测试数据集创建修复"""
    logging.info("🔧 测试数据集创建修复...")
    
    try:
        # 测试数据集创建（模拟create_trial_dataset函数的核心逻辑）
        import time as time_module
        trial_id = f"test_{int(time_module.time() * 1000) % 10000}"
        
        logging.info(f"✅ 测试trial_id生成: {trial_id}")
        
        # 验证time_module的使用
        current_time = time_module.time()
        if isinstance(current_time, float) and current_time > 0:
            logging.info("✅ time_module使用正确")
            return True
        else:
            logging.error("❌ time_module使用错误")
            return False
            
    except Exception as e:
        logging.error(f"❌ 数据集创建测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logging.info("=" * 60)
    logging.info("🚀 训练模块修复测试开始")
    logging.info(f"测试时间: {datetime.now()}")
    logging.info("=" * 60)
    
    tests = [
        ("time变量修复", test_time_variable_fix),
        ("optimizer定义修复", test_optimizer_definition),
        ("动态指标匹配修复", test_dynamic_metric_matching),
        ("数据集创建修复", test_dataset_creation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logging.info(f"\n{'='*50}")
        logging.info(f"🔧 运行测试: {test_name}")
        logging.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logging.info(f"✅ {test_name} - 通过")
            else:
                logging.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logging.error(f"❌ {test_name} - 异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试总结
    logging.info(f"\n{'='*60}")
    logging.info("📊 训练模块修复测试总结")
    logging.info(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"{test_name}: {status}")
    
    logging.info(f"\n📈 测试统计:")
    logging.info(f"  总测试数: {total}")
    logging.info(f"  通过数: {passed}")
    logging.info(f"  失败数: {total - passed}")
    logging.info(f"  成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        logging.info(f"\n🎉 所有训练模块修复测试通过！")
        return True
    else:
        logging.error(f"\n⚠️ 有 {total - passed} 个测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
