#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行处理调试测试
诊断P.pull.py中并行处理卡住的问题：
1. 并行处理性能问题
2. ts_codes_array未使用问题
3. process_stock_batch函数问题
4. 死锁和超时问题
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
import time
import signal
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor, TimeoutError
import multiprocessing

# 设置日志
log_filename = f"parallel_processing_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)

def create_test_data():
    """创建测试数据"""
    logging.info("📊 创建测试数据...")
    
    # 创建模拟股票数据
    stock_codes = [f"{i:06d}.SZ" for i in range(1, 21)]  # 20只股票
    dates = pd.date_range('2024-01-01', '2024-01-10', freq='D')  # 10天数据
    
    data = []
    for ts_code in stock_codes:
        for date in dates:
            data.append({
                'ts_code': ts_code,
                'trade_date': date.strftime('%Y%m%d'),
                'close': np.random.uniform(10, 50),
                'high': np.random.uniform(50, 60),
                'low': np.random.uniform(5, 15),
                'vol': np.random.uniform(1000, 100000),
                'open': np.random.uniform(10, 50),
                'pct_chg': np.random.uniform(-10, 10),
                'turnover_rate': np.random.uniform(0.1, 15),
                'pe': np.random.uniform(5, 100),
                'pb': np.random.uniform(0.5, 10)
            })
    
    df = pd.DataFrame(data)
    logging.info(f"✅ 创建测试数据完成: {len(df)}行, {df['ts_code'].nunique()}只股票")
    return df

def process_single_stock_simple(sub_df):
    """简化的单股票处理函数（用于测试）"""
    if sub_df.empty:
        return sub_df
    
    # 模拟一些简单的特征计算
    sub_df = sub_df.copy()
    sub_df['ma5'] = sub_df['close'].rolling(window=5).mean()
    sub_df['ma10'] = sub_df['close'].rolling(window=10).mean()
    sub_df['vol_ma5'] = sub_df['vol'].rolling(window=5).mean()
    
    # 模拟一些计算延迟
    time.sleep(0.01)  # 10ms延迟模拟计算
    
    return sub_df

def process_stock_batch_original(stock_codes, dataframe):
    """原始的process_stock_batch函数（可能有问题的版本）"""
    if dataframe.empty:
        return dataframe
    
    result_df = pd.DataFrame()
    
    # 处理每只股票
    for ts_code in stock_codes:
        try:
            # 获取单只股票的数据
            sub_df = dataframe[dataframe['ts_code'] == ts_code].copy()
            if not sub_df.empty:
                # 使用简化的处理函数
                processed_sub_df = process_single_stock_simple(sub_df)
                result_df = pd.concat([result_df, processed_sub_df], ignore_index=True)
        except Exception as e:
            logging.error(f"处理股票 {ts_code} 时出错: {e}")
            continue
    
    return result_df

def process_stock_batch_optimized(stock_codes, dataframe):
    """优化的process_stock_batch函数"""
    if dataframe.empty:
        return dataframe
    
    results = []
    
    # 处理每只股票
    for i, ts_code in enumerate(stock_codes):
        try:
            # 添加进度日志
            if i % 5 == 0:
                logging.info(f"处理批次进度: {i+1}/{len(stock_codes)} ({ts_code})")
            
            # 获取单只股票的数据
            sub_df = dataframe[dataframe['ts_code'] == ts_code].copy()
            if not sub_df.empty:
                # 使用简化的处理函数
                processed_sub_df = process_single_stock_simple(sub_df)
                results.append(processed_sub_df)
        except Exception as e:
            logging.error(f"处理股票 {ts_code} 时出错: {e}")
            continue
    
    # 一次性合并所有结果，避免多次concat
    if results:
        result_df = pd.concat(results, ignore_index=True)
    else:
        result_df = pd.DataFrame()
    
    return result_df

def test_parallel_processing_hang():
    """测试并行处理卡住问题"""
    logging.info("🔧 测试并行处理卡住问题...")
    
    df = create_test_data()
    
    # 模拟原始的并行处理逻辑
    unique_stocks = df['ts_code'].unique()
    num_cores = min(multiprocessing.cpu_count(), 4)  # 限制核心数避免过载
    batch_size = max(1, len(unique_stocks) // (num_cores * 2))
    
    logging.info(f"并行处理配置:")
    logging.info(f"  股票数量: {len(unique_stocks)}")
    logging.info(f"  处理核心: {num_cores}")
    logging.info(f"  批次大小: {batch_size}")
    
    results = []
    
    # 测试1: 原始方法（可能卡住）
    logging.info("测试1: 原始并行处理方法...")
    start_time = time.time()
    
    try:
        with ProcessPoolExecutor(max_workers=num_cores) as executor:
            # 将股票分成批次
            stock_batches = [unique_stocks[i:i + batch_size] for i in range(0, len(unique_stocks), batch_size)]
            logging.info(f"创建了{len(stock_batches)}个批次")
            
            # 提交并行任务（添加超时）
            futures = []
            for i, batch in enumerate(stock_batches):
                batch_df = df[df['ts_code'].isin(batch)]
                future = executor.submit(process_stock_batch_original, batch, batch_df)
                futures.append(future)
                logging.info(f"提交批次 {i+1}/{len(stock_batches)}: {len(batch)}只股票")
            
            # 收集结果（添加超时）
            all_results = []
            for i, future in enumerate(futures):
                try:
                    batch_result = future.result(timeout=30)  # 30秒超时
                    all_results.append(batch_result)
                    logging.info(f"完成批次 {i+1}/{len(futures)}")
                except TimeoutError:
                    logging.error(f"批次 {i+1} 超时")
                    results.append(False)
                    return False
                except Exception as e:
                    logging.error(f"批次 {i+1} 出错: {e}")
                    results.append(False)
                    return False
        
        # 合并结果
        if all_results:
            final_result = pd.concat(all_results, ignore_index=True)
            original_time = time.time() - start_time
            logging.info(f"✅ 原始方法完成: {original_time:.2f}秒, 结果行数: {len(final_result)}")
            results.append(True)
        else:
            logging.error("❌ 原始方法失败: 无结果")
            results.append(False)
            
    except Exception as e:
        logging.error(f"❌ 原始方法异常: {e}")
        results.append(False)
    
    # 测试2: 优化方法
    logging.info("测试2: 优化并行处理方法...")
    start_time = time.time()
    
    try:
        with ProcessPoolExecutor(max_workers=num_cores) as executor:
            # 将股票分成批次
            stock_batches = [unique_stocks[i:i + batch_size] for i in range(0, len(unique_stocks), batch_size)]
            
            # 提交并行任务
            futures = []
            for i, batch in enumerate(stock_batches):
                batch_df = df[df['ts_code'].isin(batch)]
                future = executor.submit(process_stock_batch_optimized, batch, batch_df)
                futures.append(future)
            
            # 收集结果
            all_results = []
            for i, future in enumerate(futures):
                try:
                    batch_result = future.result(timeout=30)
                    all_results.append(batch_result)
                    logging.info(f"优化方法完成批次 {i+1}/{len(futures)}")
                except Exception as e:
                    logging.error(f"优化方法批次 {i+1} 出错: {e}")
                    results.append(False)
                    return False
        
        # 合并结果
        if all_results:
            final_result = pd.concat(all_results, ignore_index=True)
            optimized_time = time.time() - start_time
            logging.info(f"✅ 优化方法完成: {optimized_time:.2f}秒, 结果行数: {len(final_result)}")
            results.append(True)
        else:
            logging.error("❌ 优化方法失败: 无结果")
            results.append(False)
            
    except Exception as e:
        logging.error(f"❌ 优化方法异常: {e}")
        results.append(False)
    
    return all(results)

def test_unused_variables():
    """测试未使用变量问题"""
    logging.info("🔧 测试未使用变量问题...")
    
    # 模拟ts_codes_array问题
    ts_codes = ['000001.SZ', '000002.SZ', '300001.SZ', '600001.SH', '688001.SH']
    
    # 问题代码：定义了ts_codes_array但未使用
    ts_codes_array = np.array(ts_codes)  # 未使用的变量
    ts_codes_series = pd.Series(ts_codes)  # 实际使用的变量
    
    logging.info(f"ts_codes_array定义但未使用: {type(ts_codes_array)}")
    logging.info(f"ts_codes_series实际使用: {type(ts_codes_series)}")
    
    # 检查是否可以直接删除ts_codes_array
    try:
        # 模拟后续代码只使用ts_codes_series
        result = ts_codes_series.str.len().sum()
        logging.info(f"✅ 可以安全删除ts_codes_array，只需要ts_codes_series")
        return True
    except Exception as e:
        logging.error(f"❌ 删除ts_codes_array可能有问题: {e}")
        return False

def test_memory_usage():
    """测试内存使用问题"""
    logging.info("🔧 测试内存使用问题...")
    
    import psutil
    process = psutil.Process()
    
    # 测试大数据量下的并行处理
    logging.info("创建大数据量测试...")
    
    # 创建更大的测试数据
    stock_codes = [f"{i:06d}.SZ" for i in range(1, 101)]  # 100只股票
    dates = pd.date_range('2024-01-01', '2024-01-30', freq='D')  # 30天数据
    
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    logging.info(f"初始内存使用: {initial_memory:.1f}MB")
    
    data = []
    for ts_code in stock_codes:
        for date in dates:
            data.append({
                'ts_code': ts_code,
                'trade_date': date.strftime('%Y%m%d'),
                'close': np.random.uniform(10, 50),
                'high': np.random.uniform(50, 60),
                'low': np.random.uniform(5, 15),
                'vol': np.random.uniform(1000, 100000),
                'open': np.random.uniform(10, 50),
                'pct_chg': np.random.uniform(-10, 10)
            })
    
    df = pd.DataFrame(data)
    data_memory = process.memory_info().rss / 1024 / 1024  # MB
    logging.info(f"数据创建后内存: {data_memory:.1f}MB (+{data_memory-initial_memory:.1f}MB)")
    logging.info(f"数据规模: {len(df)}行, {df['ts_code'].nunique()}只股票")
    
    # 测试是否会导致内存问题
    if data_memory - initial_memory > 500:  # 超过500MB
        logging.warning("⚠️ 内存使用过高，可能导致并行处理问题")
        return False
    else:
        logging.info("✅ 内存使用正常")
        return True

def main():
    """主函数"""
    logging.info("="*80)
    logging.info("🚀 并行处理调试测试开始")
    logging.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_filename}")
    logging.info("="*80)
    
    tests = [
        ("并行处理卡住问题", test_parallel_processing_hang),
        ("未使用变量问题", test_unused_variables),
        ("内存使用问题", test_memory_usage)
    ]
    
    results = []
    for test_name, test_func in tests:
        logging.info(f"\n{'='*60}")
        logging.info(f"🔧 运行测试: {test_name}")
        logging.info(f"{'='*60}")
        
        try:
            start_time = time.time()
            result = test_func()
            duration = time.time() - start_time
            
            results.append((test_name, result, duration))
            
            if result:
                logging.info(f"✅ {test_name} - 通过 (耗时: {duration:.2f}秒)")
            else:
                logging.error(f"❌ {test_name} - 失败 (耗时: {duration:.2f}秒)")
        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"❌ {test_name} - 测试异常: {e} (耗时: {duration:.2f}秒)")
            results.append((test_name, False, duration))
    
    # 总结报告
    logging.info(f"\n{'='*80}")
    logging.info("📊 并行处理调试测试总结")
    logging.info(f"{'='*80}")
    
    passed = sum(1 for _, result, _ in results if result)
    total = len(results)
    
    for test_name, result, duration in results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"{test_name}: {status} ({duration:.2f}秒)")
    
    logging.info(f"\n📈 测试统计:")
    logging.info(f"  总测试数: {total}")
    logging.info(f"  通过数: {passed}")
    logging.info(f"  失败数: {total - passed}")
    logging.info(f"  成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        logging.info("\n🎉 所有并行处理调试测试通过！")
        print(f"\n🎉 并行处理调试测试完全通过！日志文件: {log_filename}")
        return True
    else:
        logging.error(f"\n⚠️ {total - passed} 个测试失败，发现了问题")
        print(f"\n⚠️ 发现并行处理问题，详见日志文件: {log_filename}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logging.error(f"测试程序运行失败: {e}")
        print(f"\n❌ 测试程序异常: {e}")
        sys.exit(1)
