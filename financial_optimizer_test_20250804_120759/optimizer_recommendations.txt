金融时间序列优化器测试报告
==================================================

测试时间: 2025-08-04 12:07:59

推荐结果:
风险调整后最佳优化器: ademamix
效率最佳优化器: adamw
总结: 风险调整后最佳: ademamix, 效率最佳: adamw

详细性能数据:
------------------------------

adam:
  最终验证损失: 0.072546
  收敛速度: 68 epochs
  训练时间: 73.24 秒
  稳定性: 0.000731
  内存使用: 91.82 MB

adamw:
  最终验证损失: 0.070903
  收敛速度: 70 epochs
  训练时间: 73.28 秒
  稳定性: 0.000792
  内存使用: 48.74 MB

lion:
  最终验证损失: 0.071887
  收敛速度: 67 epochs
  训练时间: 72.01 秒
  稳定性: 0.000648
  内存使用: 36.39 MB

ademamix:
  最终验证损失: 0.068368
  收敛速度: 77 epochs
  训练时间: 69.38 秒
  稳定性: 0.001030
  内存使用: 43.37 MB

fractional:
  最终验证损失: 0.072732
  收敛速度: 73 epochs
  训练时间: 69.89 秒
  稳定性: 0.000792
  内存使用: 36.14 MB
