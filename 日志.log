2025-08-06 05:37:26,835 - INFO - 获取到 328924 条股票数据
2025-08-06 05:37:26,835 - INFO - 🔒 目标变量将在安全的数据分割流程中创建，避免数据泄漏
2025-08-06 05:37:27,290 - WARNING - 获取 20250806 的行情数据为空
2025-08-06 05:37:27,697 - INFO - 成功获取 20250805 的收盘价和名称，共 5408 条记录
2025-08-06 05:37:29.093163: I tensorflow/compiler/xla/stream_executor/cuda/cuda_gpu_executor.cc:894] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
2025-08-06 05:37:29.094890: W tensorflow/core/common_runtime/gpu/gpu_device.cc:2211] Cannot dlopen some GPU libraries. Please make sure the missing libraries mentioned above are installed properly if you would like to use GPU. Follow the guide at https://www.tensorflow.org/install/gpu for how to download and setup the required libraries for your platform.
Skipping registering GPU devices...
2025-08-06 05:37:31,323 - INFO - 内存清理完成:
清理前: 1744.9MB
清理后: 1662.5MB
释放: 82.3MB
2025-08-06 05:37:31,323 - INFO - 数据预处理完成，共处理 328924 条记录
2025-08-06 05:37:31,323 - INFO - 正在准备特征数据...
2025-08-06 05:37:34,518 - INFO - 内存清理完成:
清理前: 1693.6MB
清理后: 1670.3MB
释放: 23.2MB
2025-08-06 05:37:34,518 - INFO - 特征数据准备完成
2025-08-06 05:37:34,518 - INFO - 开始模型训练...
2025-08-06 05:37:34,518 - INFO - 初始化safe_learning_rate变量
2025-08-06 05:37:34,518 - INFO - safe_learning_rate初始值: 0.005
2025-08-06 05:37:34,518 - INFO - 开始训练模型
2025-08-06 05:37:34,518 - INFO - 
=== 开始 首板 策略训练 ===
2025-08-06 05:37:34,704 - INFO - 🔒 开始安全准备首板策略数据（无数据泄漏版本）
2025-08-06 05:37:35,949 - INFO - ✅ trade_date列已标准化为YYYY-MM-DD格式
2025-08-06 05:37:36,527 - INFO - ✅ 过滤后数据集: 328924行, 开始日期: 2025-01-01
2025-08-06 05:37:36,527 - INFO - 🔒 步骤2: 严格按时间进行数据分割（防止数据泄漏）
2025-08-06 05:37:37,136 - INFO - ✅ 修复后的时间分割（预留2天缓冲期）:
2025-08-06 05:37:37,137 - INFO -   可用数据: 141天 (总共143天)
2025-08-06 05:37:37,137 - INFO -   训练集: 2025-01-02 到 2025-06-03
2025-08-06 05:37:37,137 - INFO -   验证集: 2025-06-04 到 2025-07-02
2025-08-06 05:37:37,137 - INFO -   测试集: 2025-07-03 到 2025-08-01
2025-08-06 05:37:37,137 - INFO -   缓冲期: 2025-08-04 到 2025-08-05
2025-08-06 05:37:37,298 - INFO - 🔧 步骤2.5: 在分割前计算所有未来数据
2025-08-06 05:37:38,077 - INFO - ✅ 未来数据计算完成:
2025-08-06 05:37:38,078 - INFO -   future_1_day_pct_chg缺失: 5419个 (1.6%)
2025-08-06 05:37:38,078 - INFO -   future_2_day_pct_chg缺失: 10838个 (3.3%)
2025-08-06 05:37:38,393 - INFO - ✅ 修复后的时间分割结果:
2025-08-06 05:37:38,393 - INFO -   训练集: 83740行, 未来数据缺失: 1378个 (1.6%)
2025-08-06 05:37:38,393 - INFO -   验证集: 113622行, 未来数据缺失: 1885个 (1.7%)
2025-08-06 05:37:38,393 - INFO -   测试集: 120673行, 未来数据缺失: 1999个 (1.7%)
2025-08-06 05:37:38,393 - INFO - 🔒 步骤3: 只基于训练集计算安全特征
2025-08-06 05:37:38,502 - INFO - 🔒 步骤4: 为首板策略安全生成目标变量
2025-08-06 05:37:38,648 - INFO - ✅ 验证集/测试集中有1885个future_1_day_pct_chg为NaN (1.7%)，在合理范围内
2025-08-06 05:37:38,660 - INFO - ✅ 验证集/测试集的目标变量已准备完成，用于模型评估
2025-08-06 05:37:38,717 - INFO - ✅ 验证集/测试集中有1999个future_1_day_pct_chg为NaN (1.7%)，在合理范围内
2025-08-06 05:37:38,729 - INFO - ✅ 验证集/测试集的目标变量已准备完成，用于模型评估
2025-08-06 05:37:38,790 - INFO - 🔒 添加安全的首板策略特征...
2025-08-06 05:37:39,006 - INFO - 🔒 从训练集中安全选择首板样本，使用增强样本生成...
2025-08-06 05:37:49,372 - INFO -   增强样本生成：从3491个基础样本生成了2072个增强样本
2025-08-06 05:37:49,400 - INFO - 📊 首板样本详细统计:
2025-08-06 05:37:49,400 - INFO -   训练集总数据: 83740行
2025-08-06 05:37:49,400 - INFO -   训练集首板样本: 2072个 (2.47%)
2025-08-06 05:38:05,242 - INFO -   增强样本生成：从5103个基础样本生成了1144个增强样本
2025-08-06 05:38:21,135 - INFO -   增强样本生成：从4960个基础样本生成了1836个增强样本
2025-08-06 05:38:21,159 - INFO -   验证集总数据: 113622行
2025-08-06 05:38:21,159 - INFO -   验证集首板样本: 1144个 (1.01%)
2025-08-06 05:38:21,159 - INFO -   测试集总数据: 120673行
2025-08-06 05:38:21,159 - INFO -   测试集首板样本: 1836个 (1.52%)
2025-08-06 05:38:21,159 - INFO - ✅ 首板样本收集完成，总计5052个样本
2025-08-06 05:38:21,159 - INFO - 📊 首板策略正负样本分布统计:
2025-08-06 05:38:21,163 - INFO -   ✅ 正样本(首板成功): 0个 (0.0%)
2025-08-06 05:38:21,163 - INFO -   ❌ 负样本(首板失败): 14个 (100.0%)
2025-08-06 05:38:21,163 - INFO -   ⚪ 中性样本: 5038个
2025-08-06 05:38:21,163 - INFO -   📈 样本平衡比例: 0:14 (正:负)
2025-08-06 05:38:21,163 - WARNING -   🔴 样本分布异常：正样本=0，负样本=14
2025-08-06 05:38:21,163 - INFO - 🔒 步骤5: 准备最终的训练数据
2025-08-06 05:38:21,163 - INFO - ✅ 首板策略总样本数: 5052
2025-08-06 05:38:21,174 - INFO - 训练集目标变量分布:
future_1_day_limit_up
True     3399
False    1653
Name: count, dtype: int64
2025-08-06 05:38:21,174 - INFO - 🔒 步骤6: 准备序列数据（时间序列格式）
2025-08-06 05:38:21,174 - INFO - 🎯 超短线首板策略使用序列长度: 10天 (优化时效性)
2025-08-06 05:38:21,180 - INFO - 🔧 发现221个潜在特征列
2025-08-06 05:38:21,216 - WARNING - ⚠️ 跳过特征buy_sm_amount_mf：有效数据不足(0/5052)
2025-08-06 05:38:21,216 - WARNING - ⚠️ 跳过特征sell_sm_amount_mf：有效数据不足(0/5052)
2025-08-06 05:38:21,217 - WARNING - ⚠️ 跳过特征buy_md_amount_mf：有效数据不足(0/5052)
2025-08-06 05:38:21,217 - WARNING - ⚠️ 跳过特征sell_md_amount_mf：有效数据不足(0/5052)
2025-08-06 05:38:21,217 - WARNING - ⚠️ 跳过特征buy_lg_amount_mf：有效数据不足(0/5052)
2025-08-06 05:38:21,218 - WARNING - ⚠️ 跳过特征sell_lg_amount_mf：有效数据不足(0/5052)
2025-08-06 05:38:21,218 - WARNING - ⚠️ 跳过特征buy_elg_amount_mf：有效数据不足(0/5052)
2025-08-06 05:38:21,218 - WARNING - ⚠️ 跳过特征sell_elg_amount_mf：有效数据不足(0/5052)
2025-08-06 05:38:21,218 - WARNING - ⚠️ 跳过特征net_mf_amount_mf：有效数据不足(0/5052)
2025-08-06 05:38:21,223 - INFO - ✅ 筛选出212个有效特征
2025-08-06 05:38:21,223 - INFO - ✅ 使用pe_ttm作为pe特征
2025-08-06 05:38:21,223 - INFO - ✅ 使用214个有效特征
2025-08-06 05:38:21,224 - INFO - 🔧 开始生成序列数据，输入数据: 5052行, 序列长度: 10
2025-08-06 05:38:21,224 - INFO - 🔧 有效特征数量: 214
2025-08-06 05:38:22,389 - INFO - 🔒 步骤7: 执行最终的数据分割和返回
2025-08-06 05:38:22,390 - INFO - 🔧 数组长度统计: X=222, y1_cls=222, y1_reg=222, y2_cls=222, y2_reg=222, weights=222, 最小长度=222
2025-08-06 05:38:22,390 - INFO - ✅ 所有数组长度已统一为: 222
2025-08-06 05:38:22,390 - INFO - ✅ 最终数据形状: X=(222, 10, 214), 样本权重=222
2025-08-06 05:38:22,390 - INFO - 🔧 开始正确的时间序列分割...
2025-08-06 05:38:22,391 - INFO - 🔧 时间序列分割点:
2025-08-06 05:38:22,391 - INFO -   训练集: 2025-02-21 到 2025-07-02
2025-08-06 05:38:22,391 - INFO -   验证集: 2025-07-03 到 2025-07-15
2025-08-06 05:38:22,391 - INFO -   测试集: 2025-07-16 到 2025-07-31
2025-08-06 05:38:23,587 - INFO - 🔒 三分法安全分割完成:
2025-08-06 05:38:23,587 - INFO -   训练集: (10, 10, 214)
2025-08-06 05:38:23,587 - INFO -   验证集: (70, 10, 214)
2025-08-06 05:38:23,587 - INFO -   测试集: (142, 10, 214)
2025-08-06 05:38:23,587 - INFO - 
📊 训练集详细样本统计 (首板策略):
2025-08-06 05:38:23,588 - INFO -   总样本数: 10
2025-08-06 05:38:23,588 - INFO -   分类输出1:
2025-08-06 05:38:23,588 - INFO -     负样本(值=0): 4个 (40.0%)
2025-08-06 05:38:23,588 - INFO -     正样本(值=1): 6个 (60.0%)
2025-08-06 05:38:23,588 - INFO -     ⚠️ 样本轻度不平衡 (平衡度: 0.67)
2025-08-06 05:38:23,588 - INFO -   回归输出1:
2025-08-06 05:38:23,588 - INFO -     有效样本: 10个 (缺失: 0个)
2025-08-06 05:38:23,588 - INFO -     均值: 5.054%
2025-08-06 05:38:23,589 - INFO -     标准差: 6.691%
2025-08-06 05:38:23,589 - INFO -     范围: [-10.011%, 9.997%]
2025-08-06 05:38:23,589 - INFO -     中位数: 9.974%
2025-08-06 05:38:23,589 - INFO -   分类输出2:
2025-08-06 05:38:23,589 - INFO -     负样本(值=0): 4个 (40.0%)
2025-08-06 05:38:23,589 - INFO -     正样本(值=1): 6个 (60.0%)
2025-08-06 05:38:23,589 - INFO -     ⚠️ 样本轻度不平衡 (平衡度: 0.67)
2025-08-06 05:38:23,589 - INFO -   回归输出2:
2025-08-06 05:38:23,589 - INFO -     有效样本: 10个 (缺失: 0个)
2025-08-06 05:38:23,590 - INFO -     均值: 5.054%
2025-08-06 05:38:23,590 - INFO -     标准差: 6.691%
2025-08-06 05:38:23,590 - INFO -     范围: [-10.011%, 9.997%]
2025-08-06 05:38:23,590 - INFO -     中位数: 9.974%
2025-08-06 05:38:23,590 - INFO - 
📊 验证集详细样本统计 (首板策略):
2025-08-06 05:38:23,590 - INFO -   总样本数: 70
2025-08-06 05:38:23,590 - INFO -   分类输出1:
2025-08-06 05:38:23,590 - INFO -     负样本(值=0): 14个 (20.0%)
2025-08-06 05:38:23,590 - INFO -     正样本(值=1): 56个 (80.0%)
2025-08-06 05:38:23,591 - INFO -     🔴 样本严重不平衡 (平衡度: 0.25)
2025-08-06 05:38:23,591 - INFO -   回归输出1:
2025-08-06 05:38:23,591 - INFO -     有效样本: 70个 (缺失: 0个)
2025-08-06 05:38:23,591 - INFO -     均值: 7.466%
2025-08-06 05:38:23,591 - INFO -     标准差: 5.565%
2025-08-06 05:38:23,591 - INFO -     范围: [-10.008%, 10.120%]
2025-08-06 05:38:23,591 - INFO -     中位数: 10.000%
2025-08-06 05:38:23,591 - INFO -   分类输出2:
2025-08-06 05:38:23,592 - INFO -     负样本(值=0): 14个 (20.0%)
2025-08-06 05:38:23,592 - INFO -     正样本(值=1): 56个 (80.0%)
2025-08-06 05:38:23,592 - INFO -     🔴 样本严重不平衡 (平衡度: 0.25)
2025-08-06 05:38:23,592 - INFO -   回归输出2:
2025-08-06 05:38:23,592 - INFO -     有效样本: 70个 (缺失: 0个)
2025-08-06 05:38:23,592 - INFO -     均值: 7.466%
2025-08-06 05:38:23,592 - INFO -     标准差: 5.565%
2025-08-06 05:38:23,592 - INFO -     范围: [-10.008%, 10.120%]
2025-08-06 05:38:23,592 - INFO -     中位数: 10.000%
2025-08-06 05:38:23,592 - INFO - 
📊 测试集详细样本统计 (首板策略):
2025-08-06 05:38:23,593 - INFO -   总样本数: 142
2025-08-06 05:38:23,593 - INFO -   分类输出1:
2025-08-06 05:38:23,593 - INFO -     负样本(值=0): 17个 (12.0%)
2025-08-06 05:38:23,593 - INFO -     正样本(值=1): 125个 (88.0%)
2025-08-06 05:38:23,593 - INFO -     🔴 样本严重不平衡 (平衡度: 0.14)
2025-08-06 05:38:23,593 - INFO -   回归输出1:
2025-08-06 05:38:23,593 - INFO -     有效样本: 142个 (缺失: 0个)
2025-08-06 05:38:23,593 - INFO -     均值: 8.936%
2025-08-06 05:38:23,593 - INFO -     标准差: 3.539%
2025-08-06 05:38:23,594 - INFO -     范围: [-6.331%, 10.983%]
2025-08-06 05:38:23,594 - INFO -     中位数: 10.000%
2025-08-06 05:38:23,594 - INFO -   分类输出2:
2025-08-06 05:38:23,594 - INFO -     负样本(值=0): 17个 (12.0%)
2025-08-06 05:38:23,594 - INFO -     正样本(值=1): 125个 (88.0%)
2025-08-06 05:38:23,594 - INFO -     🔴 样本严重不平衡 (平衡度: 0.14)
2025-08-06 05:38:23,594 - INFO -   回归输出2:
2025-08-06 05:38:23,594 - INFO -     有效样本: 142个 (缺失: 0个)
2025-08-06 05:38:23,594 - INFO -     均值: 8.936%
2025-08-06 05:38:23,595 - INFO -     标准差: 3.539%
2025-08-06 05:38:23,595 - INFO -     范围: [-6.331%, 10.983%]
2025-08-06 05:38:23,595 - INFO -     中位数: 10.000%
2025-08-06 05:38:23,595 - INFO - 🔒 检查样本分布质量...
2025-08-06 05:38:23,595 - WARNING - ⚠️ 训练集的classification_output_1少数类样本较少: 4，但继续训练
2025-08-06 05:38:23,595 - INFO - 训练集 classification_output_1分布: {0: 4, 1: 6}
2025-08-06 05:38:23,595 - WARNING - ⚠️ 训练集的classification_output_2少数类样本较少: 4，但继续训练
2025-08-06 05:38:23,595 - INFO - 训练集 classification_output_2分布: {0: 4, 1: 6}
2025-08-06 05:38:23,596 - INFO - 验证集 classification_output_1分布: {0: 14, 1: 56}
2025-08-06 05:38:23,596 - INFO - 验证集 classification_output_2分布: {0: 14, 1: 56}
2025-08-06 05:38:23,596 - INFO - 测试集 classification_output_1分布: {0: 17, 1: 125}
2025-08-06 05:38:23,596 - INFO - 测试集 classification_output_2分布: {0: 17, 1: 125}
2025-08-06 05:38:23,596 - INFO - ✅ 样本分布质量检查通过
2025-08-06 05:38:23,596 - INFO -   classification_output_1 - 训练集分布: [4 6], 验证集分布: [14 56], 测试集分布: [ 17 125]
2025-08-06 05:38:23,597 - INFO -   classification_output_2 - 训练集分布: [4 6], 验证集分布: [14 56], 测试集分布: [ 17 125]
2025-08-06 05:38:23,597 - INFO - ✅ 首板策略数据准备完成，耗时: 48.89秒
2025-08-06 05:38:23,649 - INFO - 特征维度: 214个特征，10个时间步
2025-08-06 05:38:23,650 - INFO - ✅ 保持字典格式用于超参数优化，避免数据格式冲突
2025-08-06 05:38:23,650 - INFO - 开始首板策略超参数优化...
2025-08-06 05:38:23,652 - INFO - 元学习初始化完成 | 有效记录: 0条
[I 2025-08-06 05:38:23,653] A new study created in memory with name: no-name-376cdc55-c2a2-4179-943c-a8fab8aa0c85
2025-08-06 05:38:23,653 - INFO - ✅ 灵活数据验证通过: X_train(10, 10, 214), X_test(142, 10, 214)
2025-08-06 05:38:23,657 - INFO - 专家网络单元数 32 小于 首板 策略建议值 64，但保持超参数优化器的选择
2025-08-06 05:38:23,663 - INFO - 🔧 首板策略智能映射：输入214维 -> 映射到128维
2025-08-06 05:38:25,141 - INFO - 编译模型配置，策略类型: 首板, 学习率: 1.1415338890724785e-05
2025-08-06 05:38:25,143 - INFO - 使用AdEMAMix优化器 - 2024年最新技术，混合双EMA设计
2025-08-06 05:38:25,170 - INFO - 监控指标已设置为动态匹配val_classification_output_1_auc，ReduceLROnPlateau已禁用，学习率由LearningRateScheduler全权控制
2025-08-06 05:38:25,170 - INFO - y_train是字典格式，正常提取标签
2025-08-06 05:38:25,170 - INFO - 类别权重信息: {'classification_output_1': {0: 2.043729281767956, 1: 1.0}, 'classification_output_2': {0: 2.043729281767956, 1: 1.0}}
2025-08-06 05:38:25,170 - INFO - regression_output_1稳健标准化后：均值=-49.1990，中位数=0.0000，标准差=66.9077，最小值=-199.8410，最大值=0.2380
2025-08-06 05:38:25,171 - INFO - regression_output_2稳健标准化后：均值=-49.1990，中位数=0.0000，标准差=66.9077，最小值=-199.8410，最大值=0.2380
2025-08-06 05:38:25,171 - INFO - regression_output_1稳健标准化后：均值=-10.6420，中位数=0.0000，标准差=35.3866，最小值=-163.3070，最大值=9.8281
2025-08-06 05:38:25,172 - INFO - regression_output_2稳健标准化后：均值=-10.6420，中位数=0.0000，标准差=35.3866，最小值=-163.3070，最大值=9.8281
2025-08-06 05:38:25,411 - INFO - ✅ 标准方式创建数据集成功
2025-08-06 05:38:25,411 - INFO - ✅ 创建训练数据集成功，批次大小: 128, Trial: 0_6e03c090_172608
2025-08-06 05:38:25,736 - INFO - ✅ 标准方式创建数据集成功
2025-08-06 05:38:25,737 - INFO - ✅ 创建验证数据集成功，批次大小: 128, Trial: 0_6e03c090_172608
2025-08-06 05:38:25,737 - INFO - 🚀 开始首板策略超参数优化训练
2025-08-06 05:38:25,754 - INFO - 可用指标: []
2025-08-06 05:38:25,754 - INFO - 📊 训练开始时指标列表为空，将在第一个epoch后重新尝试匹配 val_classification_output_1_auc
Epoch 1/30
1/1 [==============================] - ETA: 0s - loss: 5775.1045 - classification_output_1_loss: 0.2454 - regression_output_1_loss: 1913.2037 - classification_output_2_loss: 0.3076 - regression_output_2_loss: 1946.3695 - classification_output_1_binary_accuracy: 0.7000 - classification_output_1_auc: 0.6250 - regression_output_1_mse: 6885.7412 - classification_output_2_binary_accuracy: 0.5000 - classification_output_2_auc: 0.2500 - regression_output_2_mse: 7010.20562025-08-06 05:38:51,526 - ERROR - Trial 0 失败: Unable to synchronously create dataset (name already exists)
[I 2025-08-06 05:38:51,529] Trial 0 finished with value: inf and parameters: {'param_strategy': 'history', 'lstm_units_1': 192, 'batch_size': 64, 'learning_rate': 0.01220776478695415, 'dropout_rate': 0.30000000000000004, 'l2_reg': 0.0006796578090758161, 'patience': 5, 'attention_heads': 12, 'num_experts_1': 7, 'expert_units_1': 32, 'num_experts_2': 2, 'expert_units_2': 16, 'combined_weight': 0.3042422429595377, 'base_learning_rate': 3.752055855124284e-05, 'kt_num_experts': 4, 'kt_expert_units': 60}. Best is trial 0 with value: inf.
2025-08-06 05:38:51,953 - INFO - ✅ 灵活数据验证通过: X_train(10, 10, 214), X_test(142, 10, 214)
2025-08-06 05:38:51,959 - INFO - 🔧 首板策略智能映射：输入214维 -> 映射到128维
2025-08-06 05:38:52,929 - INFO - 编译模型配置，策略类型: 首板, 学习率: 1e-06
2025-08-06 05:38:52,930 - INFO - 使用AdEMAMix优化器 - 2024年最新技术，混合双EMA设计
2025-08-06 05:38:52,952 - INFO - 监控指标已设置为动态匹配val_classification_output_1_auc，ReduceLROnPlateau已禁用，学习率由LearningRateScheduler全权控制
2025-08-06 05:38:52,952 - INFO - y_train是字典格式，正常提取标签
2025-08-06 05:38:52,952 - INFO - 类别权重信息: {'classification_output_1': {0: 2.043729281767956, 1: 1.0}, 'classification_output_2': {0: 2.043729281767956, 1: 1.0}}
2025-08-06 05:38:52,953 - INFO - regression_output_1稳健标准化后：均值=-49.1990，中位数=0.0000，标准差=66.9077，最小值=-199.8410，最大值=0.2380
2025-08-06 05:38:52,953 - INFO - regression_output_2稳健标准化后：均值=-49.1990，中位数=0.0000，标准差=66.9077，最小值=-199.8410，最大值=0.2380
2025-08-06 05:38:52,954 - INFO - regression_output_1稳健标准化后：均值=-10.6420，中位数=0.0000，标准差=35.3866，最小值=-163.3070，最大值=9.8281
2025-08-06 05:38:52,954 - INFO - regression_output_2稳健标准化后：均值=-10.6420，中位数=0.0000，标准差=35.3866，最小值=-163.3070，最大值=9.8281
2025-08-06 05:38:53,292 - INFO - ✅ 标准方式创建数据集成功
2025-08-06 05:38:53,292 - INFO - ✅ 创建训练数据集成功，批次大小: 128, Trial: 1_5260bf7d_954869
2025-08-06 05:38:53,638 - INFO - ✅ 标准方式创建数据集成功
2025-08-06 05:38:53,638 - INFO - ✅ 创建验证数据集成功，批次大小: 128, Trial: 1_5260bf7d_954869
2025-08-06 05:38:53,638 - INFO - 🚀 开始首板策略超参数优化训练
2025-08-06 05:38:53,648 - INFO - 可用指标: []
2025-08-06 05:38:53,648 - INFO - 📊 训练开始时指标列表为空，将在第一个epoch后重新尝试匹配 val_classification_output_1_auc
Epoch 1/30
1/1 [==============================] - ETA: 0s - loss: 5766.6777 - classification_output_1_loss: 0.2711 - regression_output_1_loss: 1915.8933 - classification_output_2_loss: 0.1390 - regression_output_2_loss: 1932.5813 - classification_output_1_binary_accuracy: 0.3000 - classification_output_1_auc: 0.5417 - regression_output_1_mse: 6896.0156 - classification_output_2_binary_accuracy: 0.8000 - classification_output_2_auc: 1.0000 - regression_output_2_mse: 6959.35842025-08-06 05:39:14,970 - ERROR - Trial 1 失败: Unable to synchronously create dataset (name already exists)
[I 2025-08-06 05:39:14,973] Trial 1 finished with value: inf and parameters: {'param_strategy': 'meta', 'lstm_units_1': 128, 'batch_size': 128, 'learning_rate': 0.0033327629473929496, 'dropout_rate': 0.30000000000000004, 'l2_reg': 1.5339162591163623e-06, 'patience': 14, 'attention_heads': 4, 'num_experts_1': 2, 'expert_units_1': 128, 'num_experts_2': 6, 'expert_units_2': 64, 'combined_weight': 0.3046137691733707, 'base_learning_rate': 1.9634341572933354e-06, 'kt_num_experts': 5, 'kt_expert_units': 74}. Best is trial 0 with value: inf.
2025-08-06 05:39:15,448 - INFO - ✅ 灵活数据验证通过: X_train(10, 10, 214), X_test(142, 10, 214)
2025-08-06 05:39:15,454 - INFO - 🔧 首板策略智能映射：输入214维 -> 映射到128维
2025-08-06 05:39:16,913 - INFO - 编译模型配置，策略类型: 首板, 学习率: 1e-06
2025-08-06 05:39:16,914 - INFO - 使用AdEMAMix优化器 - 2024年最新技术，混合双EMA设计
2025-08-06 05:39:16,937 - INFO - 监控指标已设置为动态匹配val_classification_output_1_auc，ReduceLROnPlateau已禁用，学习率由LearningRateScheduler全权控制
2025-08-06 05:39:16,937 - INFO - y_train是字典格式，正常提取标签
2025-08-06 05:39:16,937 - INFO - 类别权重信息: {'classification_output_1': {0: 2.043729281767956, 1: 1.0}, 'classification_output_2': {0: 2.043729281767956, 1: 1.0}}
2025-08-06 05:39:16,937 - INFO - regression_output_1稳健标准化后：均值=-49.1990，中位数=0.0000，标准差=66.9077，最小值=-199.8410，最大值=0.2380
2025-08-06 05:39:16,938 - INFO - regression_output_2稳健标准化后：均值=-49.1990，中位数=0.0000，标准差=66.9077，最小值=-199.8410，最大值=0.2380
2025-08-06 05:39:16,938 - INFO - regression_output_1稳健标准化后：均值=-10.6420，中位数=0.0000，标准差=35.3866，最小值=-163.3070，最大值=9.8281
2025-08-06 05:39:16,939 - INFO - regression_output_2稳健标准化后：均值=-10.6420，中位数=0.0000，标准差=35.3866，最小值=-163.3070，最大值=9.8281
2025-08-06 05:39:17,412 - INFO - ✅ 标准方式创建数据集成功
2025-08-06 05:39:17,412 - INFO - ✅ 创建训练数据集成功，批次大小: 128, Trial: 2_cb721a5b_939341
2025-08-06 05:39:17,884 - INFO - ✅ 标准方式创建数据集成功
2025-08-06 05:39:17,884 - INFO - ✅ 创建验证数据集成功，批次大小: 128, Trial: 2_cb721a5b_939341
2025-08-06 05:39:17,885 - INFO - 🚀 开始首板策略超参数优化训练
2025-08-06 05:39:17,896 - INFO - 可用指标: []
2025-08-06 05:39:17,896 - INFO - 📊 训练开始时指标列表为空，将在第一个epoch后重新尝试匹配 val_classification_output_1_auc
Epoch 1/30
1/1 [==============================] - ETA: 0s - loss: 5815.9629 - classification_output_1_loss: 0.3877 - regression_output_1_loss: 1926.0813 - classification_output_2_loss: 0.2307 - regression_output_2_loss: 1959.7224 - classification_output_1_binary_accuracy: 0.3000 - classification_output_1_auc: 0.1250 - regression_output_1_mse: 6935.9766 - classification_output_2_binary_accuracy: 0.5000 - classification_output_2_auc: 0.7083 - regression_output_2_mse: 7064.58742025-08-06 05:39:46,361 - ERROR - Trial 2 失败: Unable to synchronously create dataset (name already exists)
[I 2025-08-06 05:39:46,365] Trial 2 finished with value: inf and parameters: {'param_strategy': 'history', 'lstm_units_1': 256, 'batch_size': 128, 'learning_rate': 0.0034052591913244713, 'dropout_rate': 0.30000000000000004, 'l2_reg': 5.4880470007660465e-06, 'patience': 20, 'attention_heads': 10, 'num_experts_1': 8, 'expert_units_1': 128, 'num_experts_2': 4, 'expert_units_2': 64, 'combined_weight': 0.0884925020519195, 'base_learning_rate': 3.87211803217458e-06, 'kt_num_experts': 3, 'kt_expert_units': 63}. Best is trial 0 with value: inf.
2025-08-06 05:39:46,976 - ERROR - 分析失败: tuple index out of range
2025-08-06 05:39:46,980 - INFO - 已更新首板策略元学习历史记录，当前记录数: 1
2025-08-06 05:39:46,981 - INFO - 最佳试验中没有找到模型，将使用最佳参数重新构建模型
2025-08-06 05:39:47,529 - INFO - 优化完成，最佳验证损失: inf
2025-08-06 05:39:47,529 - INFO - 超参数优化耗时: 83.88秒
2025-08-06 05:39:47,529 - INFO - 最佳参数已保存到 models/首板_best_params.pkl
2025-08-06 05:39:47,530 - INFO - 首板策略最佳超参数（完整）:
2025-08-06 05:39:47,530 - INFO -   param_strategy: history
2025-08-06 05:39:47,530 - INFO -   lstm_units_1: 192
2025-08-06 05:39:47,530 - INFO -   batch_size: 64
2025-08-06 05:39:47,530 - INFO -   learning_rate: 0.01220776478695415
2025-08-06 05:39:47,530 - INFO -   dropout_rate: 0.30000000000000004
2025-08-06 05:39:47,530 - INFO -   l2_reg: 0.0006796578090758161
2025-08-06 05:39:47,530 - INFO -   patience: 5
2025-08-06 05:39:47,530 - INFO -   attention_heads: 12
2025-08-06 05:39:47,530 - INFO -   num_experts_1: 7
2025-08-06 05:39:47,530 - INFO -   expert_units_1: 32
2025-08-06 05:39:47,530 - INFO -   num_experts_2: 2
2025-08-06 05:39:47,530 - INFO -   expert_units_2: 16
2025-08-06 05:39:47,530 - INFO -   combined_weight: 0.3042422429595377
2025-08-06 05:39:47,530 - INFO -   base_learning_rate: 3.752055855124284e-05
2025-08-06 05:39:47,530 - INFO -   kt_num_experts: 4
2025-08-06 05:39:47,530 - INFO -   kt_expert_units: 60
2025-08-06 05:39:47,530 - INFO - 首板策略最佳超参数JSON格式: {
  "param_strategy": "history",
  "lstm_units_1": 192,
  "batch_size": 64,
  "learning_rate": 0.01220776478695415,
  "dropout_rate": 0.30000000000000004,
  "l2_reg": 0.0006796578090758161,
  "patience": 5,
  "attention_heads": 12,
  "num_experts_1": 7,
  "expert_units_1": 32,
  "num_experts_2": 2,
  "expert_units_2": 16,
  "combined_weight": 0.3042422429595377,
  "base_learning_rate": 3.752055855124284e-05,
  "kt_num_experts": 4,
  "kt_expert_units": 60
}
2025-08-06 05:39:47,531 - INFO - 首板策略最佳超参数: {'param_strategy': 'history', 'lstm_units_1': 192, 'batch_size': 64, 'learning_rate': 0.01220776478695415, 'dropout_rate': 0.30000000000000004, 'l2_reg': 0.0006796578090758161, 'patience': 5, 'attention_heads': 12, 'num_experts_1': 7, 'expert_units_1': 32, 'num_experts_2': 2, 'expert_units_2': 16, 'combined_weight': 0.3042422429595377, 'base_learning_rate': 3.752055855124284e-05, 'kt_num_experts': 4, 'kt_expert_units': 60}
2025-08-06 05:39:47,531 - INFO - 开始首板策略模型部署...
2025-08-06 05:39:47,531 - INFO - 超参数优化过程未返回模型，构建新模型...
2025-08-06 05:39:47,531 - INFO - 🔧 使用实际特征数量: 214，避免使用预定义的FEATURE_COLUMNS(163个)
2025-08-06 05:39:47,531 - WARNING - 特征数量过多: 214，可能导致过拟合
2025-08-06 05:39:47,531 - INFO - 专家网络单元数 32 小于 首板 策略建议值 64，但保持超参数优化器的选择
2025-08-06 05:39:47,533 - INFO - 🔧 首板策略智能映射：输入214维 -> 映射到128维
2025-08-06 05:39:48,809 - INFO - ✅ 使用优化的学习率: 0.01220776478695415
2025-08-06 05:39:48,810 - INFO - 使用AdEMAMix优化器
2025-08-06 05:39:48,834 - INFO - regression_output_1稳健标准化后：均值=-0.3838，中位数=0.0000，标准差=0.5219，最小值=-1.5588，最大值=0.0019
2025-08-06 05:39:48,834 - INFO - regression_output_2稳健标准化后：均值=-0.3838，中位数=0.0000，标准差=0.5219，最小值=-1.5588，最大值=0.0019
2025-08-06 05:39:48,835 - INFO - 无法获取股票代码信息，使用混合市场标准化
2025-08-06 05:39:48,835 - INFO - 调整混合市场regression_output_1的IQR到最小值: 16.0
2025-08-06 05:39:48,835 - INFO - 混合市场regression_output_1标准化: 范围[-1.559%, 0.002%]
2025-08-06 05:39:48,836 - INFO - 调整混合市场regression_output_2的IQR到最小值: 16.0
2025-08-06 05:39:48,836 - INFO - 混合市场regression_output_2标准化: 范围[-1.559%, 0.002%]
2025-08-06 05:39:48,836 - INFO - 混合市场regression_output_1标准化: 范围[-301.288%, 18.132%]
2025-08-06 05:39:48,837 - INFO - 混合市场regression_output_2标准化: 范围[-301.288%, 18.132%]
2025-08-06 05:39:48,837 - INFO - ✅ 已将字典格式标签转换为列表格式，匹配模型输出顺序
1/1 [==============================] - ETA: 0s - loss: 5.0650 - classification_output_1_loss: 0.3345 - regression_output_1_loss: 0.5884 - classification_output_2_loss: 0.2064 - regression_output_2_loss: 1.3367 - classification_output_1_binary_accuracy: 0.5000 - classification_output_1_classification_output_1_auc: 0.4583 - regression_output_1_mse: 0.4106 - classification_output_2_binary_accuracy: 0.4000 - classification_output_2_classification_output_2_auc: 0.5000 - regression_output_2_mse: 1.1296
Epoch 1: val_loss improved from inf to 5.61744, saving model to models/首板_model_v55_20250806_tf_5ff53295
INFO:tensorflow:Assets written to: models/首板_model_v55_20250806_tf_5ff53295/assets
2025-08-06 05:40:27,344 - INFO - Assets written to: models/首板_model_v55_20250806_tf_5ff53295/assets
1/1 [==============================] - 39s 39s/step - loss: 5.0650 - classification_output_1_loss: 0.3345 - regression_output_1_loss: 0.5884 - classification_output_2_loss: 0.2064 - regression_output_2_loss: 1.3367 - classification_output_1_binary_accuracy: 0.5000 - classification_output_1_classification_output_1_auc: 0.4583 - regression_output_1_mse: 0.4106 - classification_output_2_binary_accuracy: 0.4000 - classification_output_2_classification_output_2_auc: 0.5000 - regression_output_2_mse: 1.1296 - val_loss: 5.6174 - val_classification_output_1_loss: 0.1924 - val_regression_output_1_loss: 1.1863 - val_classification_output_2_loss: 0.1598 - val_regression_output_2_loss: 1.4930 - val_classification_output_1_binary_accuracy: 0.1831 - val_classification_output_1_classification_output_1_auc: 0.6593 - val_regression_output_1_mse: 0.6375 - val_classification_output_2_binary_accuracy: 0.4859 - val_classification_output_2_classification_output_2_auc: 0.6929 - val_regression_output_2_mse: 1.0912 - lr: 0.0122 - gpu_mem: 0.0000e+00 - cpu_mem: 2910.1992
2025-08-06 05:40:27,564 - WARNING - 使用备用指标val_classification_output_2_classification_output_2_auc作为AUC
2025-08-06 05:40:27,564 - INFO - ✅ 成功获取指标: val_classification_output_2_classification_output_2_auc = 0.6929
2025-08-06 05:40:27,564 - INFO - 策略 '首板' 训练完成。 最后一轮: 验证损失=5.6174, 验证AUC1=0.6929
INFO:tensorflow:Assets written to: models/首板_model_v55_20250806_054027_bb4qr82h/assets
2025-08-06 05:40:44,533 - INFO - Assets written to: models/首板_model_v55_20250806_054027_bb4qr82h/assets
2025-08-06 05:40:44,750 - INFO - 已保存模型: models/首板_model_v55_20250806_054027_bb4qr82h
2025-08-06 05:40:50,673 - INFO - 完成 首板 策略训练，清理内存...
2025-08-06 05:40:51,972 - INFO - 
=== 开始 连板 策略训练 ===
2025-08-06 05:40:52,812 - INFO - 🔒 开始安全准备连板策略数据（无数据泄漏版本）
2025-08-06 05:40:54,055 - INFO - ✅ trade_date列已标准化为YYYY-MM-DD格式
2025-08-06 05:40:54,625 - INFO - ✅ 过滤后数据集: 328924行, 开始日期: 2025-01-01
2025-08-06 05:40:54,625 - INFO - 🔒 步骤2: 严格按时间进行数据分割（防止数据泄漏）
2025-08-06 05:40:55,235 - INFO - ✅ 修复后的时间分割（预留2天缓冲期）:
2025-08-06 05:40:55,235 - INFO -   可用数据: 141天 (总共143天)
2025-08-06 05:40:55,235 - INFO -   训练集: 2025-01-02 到 2025-06-03
2025-08-06 05:40:55,235 - INFO -   验证集: 2025-06-04 到 2025-07-02
2025-08-06 05:40:55,235 - INFO -   测试集: 2025-07-03 到 2025-08-01
2025-08-06 05:40:55,235 - INFO -   缓冲期: 2025-08-04 到 2025-08-05
2025-08-06 05:40:55,371 - INFO - 🔧 步骤2.5: 在分割前计算所有未来数据
2025-08-06 05:40:56,145 - INFO - ✅ 未来数据计算完成:
2025-08-06 05:40:56,145 - INFO -   future_1_day_pct_chg缺失: 5419个 (1.6%)
2025-08-06 05:40:56,146 - INFO -   future_2_day_pct_chg缺失: 10838个 (3.3%)
2025-08-06 05:40:56,463 - INFO - ✅ 修复后的时间分割结果:
2025-08-06 05:40:56,463 - INFO -   训练集: 83740行, 未来数据缺失: 1378个 (1.6%)
2025-08-06 05:40:56,463 - INFO -   验证集: 113622行, 未来数据缺失: 1885个 (1.7%)
2025-08-06 05:40:56,463 - INFO -   测试集: 120673行, 未来数据缺失: 1999个 (1.7%)
2025-08-06 05:40:56,463 - INFO - 🔒 步骤3: 只基于训练集计算安全特征
2025-08-06 05:40:56,563 - INFO - 🔒 步骤4: 为连板策略安全生成目标变量
2025-08-06 05:40:56,704 - INFO - ✅ 验证集/测试集中有1885个future_1_day_pct_chg为NaN (1.7%)，在合理范围内
2025-08-06 05:40:56,716 - INFO - ✅ 验证集/测试集的目标变量已准备完成，用于模型评估
2025-08-06 05:40:56,772 - INFO - ✅ 验证集/测试集中有1999个future_1_day_pct_chg为NaN (1.7%)，在合理范围内
2025-08-06 05:40:56,784 - INFO - ✅ 验证集/测试集的目标变量已准备完成，用于模型评估
2025-08-06 05:40:56,784 - INFO - 🔒 处理连板策略（安全版本）...
2025-08-06 05:40:57,037 - WARNING -   连板增强样本生成失败，回退到基础样本
2025-08-06 05:40:57,037 - WARNING -   连板增强样本生成失败，回退到基础样本
2025-08-06 05:40:57,038 - WARNING -   连板增强样本生成失败，回退到基础样本
2025-08-06 05:40:57,038 - INFO - ✅ 训练集中找到0个有效连板样本
2025-08-06 05:40:57,038 - INFO - ✅ 验证集中找到0个连板样本
2025-08-06 05:40:57,038 - INFO - ✅ 测试集中找到0个连板样本
2025-08-06 05:40:57,040 - INFO - 🔒 步骤5: 准备最终的训练数据
2025-08-06 05:40:57,040 - WARNING - ❌ 连板策略没有足够的样本数据
2025-08-06 05:40:57,116 - WARNING - 连板策略没有足够的训练数据
2025-08-06 05:40:57,117 - INFO - 模型训练总耗时: 202.60秒
2025-08-06 05:41:03,272 - INFO - 内存清理完成:
清理前: 3180.7MB
清理后: 2956.7MB
释放: 224.0MB