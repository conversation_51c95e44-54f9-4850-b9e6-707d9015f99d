#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志问题修复的脚本
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
import re

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_enhanced_data_validation():
    """测试增强的数据验证函数"""
    logging.info("测试增强的数据验证...")
    
    try:
        # 模拟数据验证函数
        def enhanced_data_validation(X_train, X_test, y_train, y_test):
            """增强的数据验证和转换函数"""
            try:
                # 确保X数据是numpy数组
                if not isinstance(X_train, np.ndarray):
                    logging.warning(f"X_train类型错误({type(X_train)})，转换为numpy数组")
                    X_train = np.array(X_train)
                if not isinstance(X_test, np.ndarray):
                    logging.warning(f"X_test类型错误({type(X_test)})，转换为numpy数组")
                    X_test = np.array(X_test)
                
                # 验证X数据的形状
                if not hasattr(X_train, 'shape'):
                    raise ValueError(f"X_train没有shape属性，类型: {type(X_train)}")
                if not hasattr(X_test, 'shape'):
                    raise ValueError(f"X_test没有shape属性，类型: {type(X_test)}")
                
                if len(X_train.shape) != 3:
                    raise ValueError(f"X_train应该是3维数组，实际形状: {X_train.shape}")
                if len(X_test.shape) != 3:
                    raise ValueError(f"X_test应该是3维数组，实际形状: {X_test.shape}")
                
                # 验证y数据格式
                if not isinstance(y_train, dict):
                    raise ValueError(f"y_train应该是字典格式，实际类型: {type(y_train)}")
                if not isinstance(y_test, dict):
                    raise ValueError(f"y_test应该是字典格式，实际类型: {type(y_test)}")
                
                # 验证y数据的键
                required_keys = ['classification_output_1', 'regression_output_1', 'classification_output_2', 'regression_output_2']
                for key in required_keys:
                    if key not in y_train:
                        raise ValueError(f"y_train缺少必需的键: {key}")
                    if key not in y_test:
                        raise ValueError(f"y_test缺少必需的键: {key}")
                    
                    # 确保y数据是numpy数组
                    if not isinstance(y_train[key], np.ndarray):
                        y_train[key] = np.array(y_train[key])
                    if not isinstance(y_test[key], np.ndarray):
                        y_test[key] = np.array(y_test[key])
                
                logging.info(f"✅ 数据验证通过: X_train{X_train.shape}, X_test{X_test.shape}")
                return X_train, X_test, y_train, y_test
                
            except Exception as e:
                logging.error(f"❌ 数据验证失败: {str(e)}")
                raise e
        
        # 测试正常情况
        X_train = np.random.random((100, 10, 50))
        X_test = np.random.random((20, 10, 50))
        y_train = {
            'classification_output_1': np.random.randint(0, 2, 100),
            'regression_output_1': np.random.random(100),
            'classification_output_2': np.random.randint(0, 2, 100),
            'regression_output_2': np.random.random(100)
        }
        y_test = {
            'classification_output_1': np.random.randint(0, 2, 20),
            'regression_output_1': np.random.random(20),
            'classification_output_2': np.random.randint(0, 2, 20),
            'regression_output_2': np.random.random(20)
        }
        
        X_train, X_test, y_train, y_test = enhanced_data_validation(X_train, X_test, y_train, y_test)
        logging.info("✅ 正常数据验证通过")
        
        # 测试list转换
        X_train_list = X_train.tolist()
        X_test_list = X_test.tolist()
        
        X_train, X_test, y_train, y_test = enhanced_data_validation(X_train_list, X_test_list, y_train, y_test)
        logging.info("✅ list转numpy数组验证通过")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 数据验证测试失败: {e}")
        return False

def test_dynamic_auc_metric():
    """测试动态AUC指标获取"""
    logging.info("测试动态AUC指标获取...")
    
    try:
        def get_dynamic_auc_metric(history):
            """动态获取AUC指标，支持多轮训练的不同后缀"""
            import re
            
            # 定义AUC指标的正则表达式模式
            auc_patterns = [
                r'val_classification_output_1_auc_?\d*$',
                r'val_auc_?\d*$',
                r'val_binary_accuracy_?\d*$'
            ]
            
            # 搜索匹配的AUC指标
            for pattern in auc_patterns:
                matching_keys = [k for k in history.keys() if re.match(pattern, k)]
                if matching_keys:
                    # 选择最新的指标（按名称排序，取最后一个）
                    latest_key = sorted(matching_keys)[-1]
                    values = history[latest_key]
                    if values and len(values) > 0:
                        return values[-1], latest_key
            
            # 如果都没找到，使用accuracy作为替代
            acc_patterns = [
                r'val_classification_output_1_accuracy_?\d*$', 
                r'val_accuracy_?\d*$'
            ]
            for pattern in acc_patterns:
                matching_keys = [k for k in history.keys() if re.match(pattern, k)]
                if matching_keys:
                    latest_key = sorted(matching_keys)[-1]
                    values = history[latest_key]
                    if values and len(values) > 0:
                        logging.info(f"使用{latest_key}作为AUC替代指标")
                        return values[-1], latest_key
            
            # 最后尝试所有包含'auc'或'accuracy'的指标
            fallback_keys = [k for k in history.keys() if 'auc' in k.lower() or 'accuracy' in k.lower()]
            if fallback_keys:
                latest_key = sorted(fallback_keys)[-1]
                values = history[latest_key]
                if values and len(values) > 0:
                    logging.warning(f"使用备用指标{latest_key}作为AUC")
                    return values[-1], latest_key
            
            # 如果真的找不到任何指标，抛出异常而不是使用默认值
            available_keys = list(history.keys())
            raise ValueError(f"无法找到任何有效的AUC或accuracy指标。可用指标: {available_keys}")
        
        # 测试不同的历史记录格式
        test_cases = [
            {
                'name': '标准AUC指标',
                'history': {
                    'val_classification_output_1_auc': [0.6, 0.7, 0.75],
                    'val_loss': [1.5, 1.2, 1.0]
                },
                'expected_value': 0.75,
                'expected_key': 'val_classification_output_1_auc'
            },
            {
                'name': '带后缀的AUC指标',
                'history': {
                    'val_classification_output_1_auc_2': [0.6, 0.7, 0.8],
                    'val_classification_output_1_auc_1': [0.5, 0.6, 0.7],
                    'val_loss': [1.5, 1.2, 1.0]
                },
                'expected_value': 0.8,
                'expected_key': 'val_classification_output_1_auc_2'
            },
            {
                'name': '只有accuracy指标',
                'history': {
                    'val_classification_output_1_accuracy': [0.6, 0.7, 0.85],
                    'val_loss': [1.5, 1.2, 1.0]
                },
                'expected_value': 0.85,
                'expected_key': 'val_classification_output_1_accuracy'
            }
        ]
        
        for test_case in test_cases:
            try:
                value, key = get_dynamic_auc_metric(test_case['history'])
                if abs(value - test_case['expected_value']) < 1e-6 and key == test_case['expected_key']:
                    logging.info(f"✅ {test_case['name']}: 通过 (值={value}, 键={key})")
                else:
                    logging.error(f"❌ {test_case['name']}: 失败 (期望值={test_case['expected_value']}, 实际值={value})")
                    return False
            except Exception as e:
                logging.error(f"❌ {test_case['name']}: 异常 {e}")
                return False
        
        # 测试异常情况
        try:
            get_dynamic_auc_metric({'val_loss': [1.5, 1.2, 1.0]})
            logging.error("❌ 应该抛出异常但没有")
            return False
        except ValueError:
            logging.info("✅ 正确抛出异常当没有找到AUC指标时")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 动态AUC指标测试失败: {e}")
        return False

def test_sequence_length_config():
    """测试序列长度配置"""
    logging.info("测试序列长度配置...")
    
    try:
        SEQUENCE_LENGTH_CONFIG = {
            '首板': 10,  # 首板需要10天历史
            '连板': 15,  # 连板需要15天历史
        }
        
        # 测试不同策略的序列长度
        test_cases = [
            ('首板', 10),
            ('连板', 15),
            ('未知策略', 10)  # 默认值
        ]
        
        for strategy_type, expected_length in test_cases:
            sequence_length = SEQUENCE_LENGTH_CONFIG.get(strategy_type, 10)
            if sequence_length == expected_length:
                logging.info(f"✅ {strategy_type}策略序列长度正确: {sequence_length}")
            else:
                logging.error(f"❌ {strategy_type}策略序列长度错误: 期望{expected_length}, 实际{sequence_length}")
                return False
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 序列长度配置测试失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("开始测试日志问题修复...")
    
    tests = [
        ("增强数据验证", test_enhanced_data_validation),
        ("动态AUC指标获取", test_dynamic_auc_metric),
        ("序列长度配置", test_sequence_length_config)
    ]
    
    results = []
    for test_name, test_func in tests:
        logging.info(f"\n{'='*50}")
        logging.info(f"运行测试: {test_name}")
        logging.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logging.info(f"✅ {test_name} - 通过")
            else:
                logging.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logging.error(f"❌ {test_name} - 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    logging.info(f"\n{'='*50}")
    logging.info("测试总结")
    logging.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"{test_name}: {status}")
    
    logging.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有日志问题修复测试通过！")
        return True
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logging.error(f"测试运行失败: {e}")
        print(f"\n❌ 测试运行失败: {e}")
        sys.exit(1)
