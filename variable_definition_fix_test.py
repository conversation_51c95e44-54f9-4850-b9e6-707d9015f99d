#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
变量定义修复测试
验证P.pull.py中的变量定义问题修复：
1. process_stock_batch函数恢复
2. stock变量引用修复
3. 函数调用完整性验证
"""

import sys
import os
import re
import logging
import ast
from datetime import datetime

# 设置日志
log_filename = f"variable_definition_fix_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)

def read_file_content(file_path):
    """读取文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logging.error(f"读取文件失败: {e}")
        return None

def test_process_stock_batch_restored():
    """测试process_stock_batch函数是否已恢复"""
    logging.info("🔧 测试process_stock_batch函数恢复...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    results = []
    
    # 1. 检查函数定义
    function_def = len(re.findall(r'def process_stock_batch\(', content))
    if function_def == 1:
        logging.info("✅ process_stock_batch函数定义已恢复")
        results.append(True)
    else:
        logging.error(f"❌ process_stock_batch函数定义问题: count={function_def}")
        results.append(False)
    
    # 2. 检查函数调用
    function_call = len(re.findall(r'executor\.submit\(process_stock_batch', content))
    if function_call >= 1:
        logging.info("✅ process_stock_batch函数调用存在")
        results.append(True)
    else:
        logging.error("❌ process_stock_batch函数调用缺失")
        results.append(False)
    
    # 3. 检查函数参数
    if 'def process_stock_batch(stock_codes, dataframe):' in content:
        logging.info("✅ process_stock_batch函数参数正确")
        results.append(True)
    else:
        logging.error("❌ process_stock_batch函数参数不正确")
        results.append(False)
    
    # 4. 检查函数内容
    if 'process_single_stock(sub_df)' in content:
        logging.info("✅ process_stock_batch函数调用process_single_stock")
        results.append(True)
    else:
        logging.error("❌ process_stock_batch函数内容不完整")
        results.append(False)
    
    return all(results)

def test_undefined_variables():
    """测试未定义变量问题"""
    logging.info("🔧 测试未定义变量修复...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    results = []
    
    # 1. 检查stock变量引用
    stock_references = re.findall(r"stock\['[^']+'\]", content)
    if not stock_references:
        logging.info("✅ 没有发现未定义的stock变量引用")
        results.append(True)
    else:
        logging.error(f"❌ 发现未定义的stock变量引用: {stock_references}")
        results.append(False)
    
    # 2. 检查ts_code变量使用
    ts_code_usage = len(re.findall(r'ts_code', content))
    if ts_code_usage >= 50:  # 应该有很多ts_code的使用
        logging.info(f"✅ ts_code变量使用正常: {ts_code_usage}次")
        results.append(True)
    else:
        logging.warning(f"⚠️ ts_code变量使用较少: {ts_code_usage}次")
        results.append(False)
    
    # 3. 检查特定的修复位置
    if 'logging.warning(f"获取{ts_code}数据失败: {e}")' in content:
        logging.info("✅ 第544行的stock变量引用已修复为ts_code")
        results.append(True)
    else:
        logging.error("❌ 第544行的stock变量引用未修复")
        results.append(False)
    
    return all(results)

def test_function_call_integrity():
    """测试函数调用完整性"""
    logging.info("🔧 测试函数调用完整性...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    results = []
    
    # 定义函数和其调用的映射
    function_calls = {
        'process_stock_batch': [r'executor\.submit\(process_stock_batch'],
        'process_single_stock': [r'process_single_stock\('],
        'generate_unified_shouban_samples': [r'generate_unified_shouban_samples\('],
        'generate_unified_lianban_samples': [r'generate_unified_lianban_samples\('],
        'add_features': [r'add_features\('],
        'predict_and_select': [r'predict_and_select\(']
    }
    
    for func_name, call_patterns in function_calls.items():
        # 检查函数定义
        func_def = len(re.findall(f'def {func_name}\\(', content))
        
        # 检查函数调用
        total_calls = 0
        for pattern in call_patterns:
            calls = len(re.findall(pattern, content))
            total_calls += calls
        
        if func_def >= 1 and total_calls >= 1:
            logging.info(f"✅ {func_name}: 定义{func_def}次, 调用{total_calls}次")
            results.append(True)
        elif func_def >= 1 and total_calls == 0:
            logging.warning(f"⚠️ {func_name}: 有定义但无调用")
            results.append(False)
        elif func_def == 0 and total_calls >= 1:
            logging.error(f"❌ {func_name}: 有调用但无定义")
            results.append(False)
        else:
            logging.error(f"❌ {func_name}: 既无定义也无调用")
            results.append(False)
    
    return all(results)

def test_syntax_validation():
    """测试语法验证"""
    logging.info("🔧 测试Python语法验证...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    try:
        # 尝试解析Python语法
        ast.parse(content)
        logging.info("✅ Python语法验证通过")
        return True
    except SyntaxError as e:
        logging.error(f"❌ Python语法错误: {e}")
        logging.error(f"错误位置: 行{e.lineno}, 列{e.offset}")
        return False
    except Exception as e:
        logging.error(f"❌ 语法验证异常: {e}")
        return False

def test_import_completeness():
    """测试导入完整性"""
    logging.info("🔧 测试导入完整性...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    results = []
    
    # 检查关键导入
    required_imports = {
        'pandas': r'import pandas|from pandas',
        'numpy': r'import numpy|from numpy',
        'tensorflow': r'import tensorflow|from tensorflow',
        'concurrent.futures': r'from concurrent\.futures import ProcessPoolExecutor'
    }
    
    for import_name, pattern in required_imports.items():
        if re.search(pattern, content):
            logging.info(f"✅ {import_name} 导入存在")
            results.append(True)
        else:
            logging.error(f"❌ {import_name} 导入缺失")
            results.append(False)
    
    return all(results)

def main():
    """主函数"""
    logging.info("="*80)
    logging.info("🚀 变量定义修复测试开始")
    logging.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_filename}")
    logging.info("="*80)
    
    tests = [
        ("process_stock_batch函数恢复", test_process_stock_batch_restored),
        ("未定义变量修复", test_undefined_variables),
        ("函数调用完整性", test_function_call_integrity),
        ("Python语法验证", test_syntax_validation),
        ("导入完整性", test_import_completeness)
    ]
    
    results = []
    for test_name, test_func in tests:
        logging.info(f"\n{'='*60}")
        logging.info(f"🔧 运行测试: {test_name}")
        logging.info(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logging.info(f"✅ {test_name} - 通过")
            else:
                logging.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logging.error(f"❌ {test_name} - 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结报告
    logging.info(f"\n{'='*80}")
    logging.info("📊 变量定义修复测试总结")
    logging.info(f"{'='*80}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"{test_name}: {status}")
    
    logging.info(f"\n📈 测试统计:")
    logging.info(f"  总测试数: {total}")
    logging.info(f"  通过数: {passed}")
    logging.info(f"  失败数: {total - passed}")
    logging.info(f"  成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        logging.info("\n🎉 所有变量定义修复测试通过！")
        logging.info("✅ process_stock_batch函数已恢复")
        logging.info("✅ 未定义变量已修复")
        logging.info("✅ 函数调用完整性已验证")
        logging.info("✅ Python语法正确")
        logging.info("✅ 导入完整性已验证")
        print(f"\n🎉 变量定义修复测试完全通过！日志文件: {log_filename}")
        return True
    else:
        logging.error(f"\n⚠️ {total - passed} 个测试失败，需要进一步修复")
        print(f"\n⚠️ 部分修复测试失败，详见日志文件: {log_filename}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logging.error(f"测试程序运行失败: {e}")
        print(f"\n❌ 测试程序异常: {e}")
        sys.exit(1)
