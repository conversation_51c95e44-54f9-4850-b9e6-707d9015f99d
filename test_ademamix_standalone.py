#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AdEMAMix优化器独立测试
基于原文结构的场景测试，验证AdEMAMix优化器的正确性和性能
"""

import numpy as np
import tensorflow as tf
import logging
import time
from typing import Dict, List, Tuple, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# -------------------- AdEMAMix优化器定义 --------------------
@tf.keras.utils.register_keras_serializable(package='custom_optimizers', name='AdEMAMix')
class AdEMAMix(tf.keras.optimizers.Adam):
    """
    AdEMAMix优化器实现（基于Adam的扩展版本）
    
    基于论文: "The AdEMAMix Optimizer: Better, Faster, Older" (2024)
    核心思想：混合两个不同时间尺度的指数移动平均来解决单一EMA的局限性
    """
    
    def __init__(self, learning_rate=1e-4, beta_1=0.9, beta_2=0.999,
                 alpha=0.5, epsilon=1e-8, weight_decay=0.0,
                 name="AdEMAMix", **kwargs):
        """
        初始化AdEMAMix优化器
        
        Args:
            learning_rate: 学习率
            beta_1: 第一个EMA的衰减率（短期）
            beta_2: 二阶矩估计的衰减率
            alpha: 两个EMA的混合权重
            epsilon: 数值稳定性常数
            weight_decay: 权重衰减系数
        """
        # 使用Adam作为基础，添加AdEMAMix的特性
        super().__init__(
            learning_rate=learning_rate,
            beta_1=beta_1,
            beta_2=beta_2,
            epsilon=epsilon,
            name=name,
            **kwargs
        )
        
        # AdEMAMix特有参数
        self.alpha = alpha
        self.weight_decay = weight_decay
        self.beta_long = 0.999  # 长期EMA的衰减率
        
        # 用于存储长期EMA的变量
        self._long_term_ema = {}
    
    def apply_gradients(self, grads_and_vars, **kwargs):
        """
        应用梯度更新，实现AdEMAMix的核心逻辑
        """
        # 确保grads_and_vars是一个列表
        grads_and_vars = list(grads_and_vars)

        # AdEMAMix的额外逻辑：维护长期EMA（在Adam更新之前）
        for item in grads_and_vars:
            if len(item) == 2:
                grad, var = item
                if grad is not None:
                    var_name = var.name

                    # 初始化长期EMA（如果不存在）
                    if var_name not in self._long_term_ema:
                        try:
                            # 创建安全的变量名
                            safe_name = var_name.replace('/', '_').replace(':', '_').replace('[', '_').replace(']', '_')
                            self._long_term_ema[var_name] = tf.Variable(
                                tf.zeros_like(grad),
                                trainable=False,
                                name=f"long_ema_{safe_name}"
                            )
                            logging.debug(f"AdEMAMix: 为变量 {var_name} 创建长期EMA")
                        except Exception as e:
                            logging.debug(f"AdEMAMix: 无法为变量 {var_name} 创建长期EMA: {e}")
                            continue

                    # 更新长期EMA
                    try:
                        long_ema = self._long_term_ema[var_name]
                        # 检查形状和数据类型是否匹配
                        if (long_ema.shape == grad.shape and
                            long_ema.dtype == grad.dtype and
                            tf.reduce_all(tf.math.is_finite(grad))):
                            # 安全的EMA更新
                            new_ema = self.beta_long * long_ema + (1 - self.beta_long) * grad
                            long_ema.assign(new_ema)
                        else:
                            # 形状或数据类型不匹配时，重新初始化
                            long_ema.assign(grad * 0.1)
                            logging.debug(f"AdEMAMix: 重新初始化变量 {var_name} 的长期EMA")

                    except Exception as e:
                        # 如果仍然出错，跳过这个变量的长期EMA更新
                        logging.debug(f"AdEMAMix: 跳过变量 {var_name} 的长期EMA更新，错误: {e}")
                        continue

        # 然后应用标准Adam更新
        result = super().apply_gradients(grads_and_vars, **kwargs)

        # 应用权重衰减（如果启用）
        if self.weight_decay > 0:
            for item in grads_and_vars:
                if len(item) == 2:
                    grad, var = item
                    if grad is not None:
                        var.assign_sub(self.learning_rate * self.weight_decay * var)

        return result
    
    def get_config(self):
        """获取优化器配置"""
        config = super().get_config()
        config.update({
            'alpha': self.alpha,
            'weight_decay': self.weight_decay,
        })
        return config
    
    @classmethod
    def from_config(cls, config, custom_objects=None):
        """从配置创建优化器实例"""
        return cls(**config)


class AdEMAMixScenarioTester:
    """AdEMAMix优化器场景测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        
    def scenario_1_optimizer_initialization(self) -> bool:
        """场景1: 优化器初始化和参数验证"""
        logging.info("🧪 场景1: AdEMAMix优化器初始化测试")
        
        try:
            # 测试默认参数初始化
            optimizer_default = AdEMAMix()
            assert optimizer_default.learning_rate == 1e-4
            assert optimizer_default.beta_1 == 0.9
            assert optimizer_default.beta_2 == 0.999
            assert optimizer_default.alpha == 0.5
            assert optimizer_default.epsilon == 1e-8
            assert optimizer_default.weight_decay == 0.0
            logging.info("✅ 默认参数初始化正确")
            
            # 测试自定义参数初始化
            optimizer_custom = AdEMAMix(
                learning_rate=1e-3,
                beta_1=0.95,
                beta_2=0.99,
                alpha=0.3,
                epsilon=1e-7,
                weight_decay=0.01
            )
            assert optimizer_custom.learning_rate == 1e-3
            assert optimizer_custom.alpha == 0.3
            assert optimizer_custom.weight_decay == 0.01
            logging.info("✅ 自定义参数初始化正确")
            
            # 测试长期EMA参数
            assert optimizer_custom.beta_long == 0.999
            assert isinstance(optimizer_custom._long_term_ema, dict)
            logging.info("✅ 长期EMA参数初始化正确")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 场景1失败: {e}")
            return False
    
    def scenario_2_gradient_update_mechanism(self) -> bool:
        """场景2: 梯度更新机制验证"""
        logging.info("🧪 场景2: AdEMAMix梯度更新机制测试")
        
        try:
            # 创建简单的测试模型
            model = tf.keras.Sequential([
                tf.keras.layers.Dense(10, input_shape=(5,)),
                tf.keras.layers.Dense(1)
            ])
            
            # 创建AdEMAMix优化器
            optimizer = AdEMAMix(learning_rate=1e-3)
            
            # 生成测试数据
            x_test = tf.random.normal((32, 5))
            y_test = tf.random.normal((32, 1))
            
            # 记录初始权重
            initial_weights = [w.numpy().copy() for w in model.trainable_variables]
            
            # 执行一次前向传播和反向传播
            with tf.GradientTape() as tape:
                predictions = model(x_test, training=True)
                loss = tf.keras.losses.mse(y_test, predictions)
            
            gradients = tape.gradient(loss, model.trainable_variables)
            
            # 应用梯度更新
            optimizer.apply_gradients(zip(gradients, model.trainable_variables))
            
            # 验证权重已更新
            updated_weights = [w.numpy() for w in model.trainable_variables]
            weights_changed = any(
                not np.allclose(initial, updated, atol=1e-8)
                for initial, updated in zip(initial_weights, updated_weights)
            )
            
            assert weights_changed, "权重应该在梯度更新后发生变化"
            logging.info("✅ 梯度更新机制正常工作")
            
            # 验证长期EMA已创建
            assert len(optimizer._long_term_ema) > 0, "长期EMA应该已创建"
            logging.info(f"✅ 长期EMA已创建，包含 {len(optimizer._long_term_ema)} 个变量")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 场景2失败: {e}")
            return False
    
    def scenario_3_long_term_ema_maintenance(self) -> bool:
        """场景3: 长期EMA维护测试"""
        logging.info("🧪 场景3: 长期EMA维护测试")
        
        try:
            # 创建测试模型
            model = tf.keras.Sequential([
                tf.keras.layers.Dense(5, input_shape=(3,)),
                tf.keras.layers.Dense(1)
            ])
            
            optimizer = AdEMAMix(learning_rate=1e-3)
            optimizer.beta_long = 0.99  # 直接设置beta_long参数
            
            # 生成测试数据
            x_test = tf.random.normal((16, 3))
            y_test = tf.random.normal((16, 1))
            
            # 执行多次更新以测试EMA维护
            ema_values = []
            for step in range(5):
                with tf.GradientTape() as tape:
                    predictions = model(x_test, training=True)
                    loss = tf.keras.losses.mse(y_test, predictions)
                
                gradients = tape.gradient(loss, model.trainable_variables)
                optimizer.apply_gradients(zip(gradients, model.trainable_variables))
                
                # 记录EMA值
                if optimizer._long_term_ema:
                    first_var_name = list(optimizer._long_term_ema.keys())[0]
                    ema_value = optimizer._long_term_ema[first_var_name].numpy().copy()
                    ema_values.append(ema_value)
            
            # 验证EMA值在变化
            assert len(ema_values) >= 2, "应该有多个EMA值记录"
            
            # 验证EMA值确实在更新
            ema_changed = not np.allclose(ema_values[0], ema_values[-1], atol=1e-8)
            assert ema_changed, "长期EMA值应该在多次更新后发生变化"
            
            logging.info("✅ 长期EMA维护机制正常工作")
            return True
            
        except Exception as e:
            logging.error(f"❌ 场景3失败: {e}")
            return False
    
    def scenario_4_performance_comparison(self) -> bool:
        """场景4: 与Adam优化器性能对比"""
        logging.info("🧪 场景4: AdEMAMix vs Adam性能对比测试")
        
        try:
            # 创建相同的测试问题
            def create_test_model():
                return tf.keras.Sequential([
                    tf.keras.layers.Dense(20, activation='relu', input_shape=(10,)),
                    tf.keras.layers.Dense(10, activation='relu'),
                    tf.keras.layers.Dense(1)
                ])
            
            # 生成测试数据
            np.random.seed(42)
            tf.random.set_seed(42)
            x_train = tf.random.normal((1000, 10))
            y_train = tf.reduce_sum(x_train[:, :5], axis=1, keepdims=True) + tf.random.normal((1000, 1)) * 0.1
            
            # 测试AdEMAMix
            model_ademamix = create_test_model()
            optimizer_ademamix = AdEMAMix(learning_rate=1e-3)
            
            losses_ademamix = []
            for epoch in range(10):
                with tf.GradientTape() as tape:
                    predictions = model_ademamix(x_train, training=True)
                    loss = tf.keras.losses.mse(y_train, predictions)
                
                gradients = tape.gradient(loss, model_ademamix.trainable_variables)
                optimizer_ademamix.apply_gradients(zip(gradients, model_ademamix.trainable_variables))
                losses_ademamix.append(float(tf.reduce_mean(loss)))
            
            # 测试Adam
            tf.random.set_seed(42)  # 重置随机种子确保公平比较
            model_adam = create_test_model()
            optimizer_adam = tf.keras.optimizers.Adam(learning_rate=1e-3)
            
            losses_adam = []
            for epoch in range(10):
                with tf.GradientTape() as tape:
                    predictions = model_adam(x_train, training=True)
                    loss = tf.keras.losses.mse(y_train, predictions)
                
                gradients = tape.gradient(loss, model_adam.trainable_variables)
                optimizer_adam.apply_gradients(zip(gradients, model_adam.trainable_variables))
                losses_adam.append(float(tf.reduce_mean(loss)))
            
            # 比较最终损失
            final_loss_ademamix = losses_ademamix[-1]
            final_loss_adam = losses_adam[-1]
            
            logging.info(f"AdEMAMix最终损失: {final_loss_ademamix:.6f}")
            logging.info(f"Adam最终损失: {final_loss_adam:.6f}")
            
            # 存储性能指标
            self.performance_metrics['ademamix_losses'] = losses_ademamix
            self.performance_metrics['adam_losses'] = losses_adam
            
            # 验证AdEMAMix至少不比Adam差太多
            performance_ratio = final_loss_ademamix / final_loss_adam
            assert performance_ratio < 2.0, f"AdEMAMix性能不应该比Adam差太多，比率: {performance_ratio}"
            
            logging.info("✅ AdEMAMix与Adam性能对比测试通过")
            return True
            
        except Exception as e:
            logging.error(f"❌ 场景4失败: {e}")
            return False
    
    def run_all_scenarios(self) -> Dict[str, bool]:
        """运行所有场景测试"""
        logging.info("🚀 开始AdEMAMix优化器场景测试")
        
        scenarios = [
            ("优化器初始化", self.scenario_1_optimizer_initialization),
            ("梯度更新机制", self.scenario_2_gradient_update_mechanism),
            ("长期EMA维护", self.scenario_3_long_term_ema_maintenance),
            ("性能对比", self.scenario_4_performance_comparison),
        ]
        
        results = {}
        passed_count = 0
        
        for name, test_func in scenarios:
            try:
                result = test_func()
                results[name] = result
                if result:
                    passed_count += 1
                    logging.info(f"✅ {name} - 通过")
                else:
                    logging.error(f"❌ {name} - 失败")
            except Exception as e:
                results[name] = False
                logging.error(f"❌ {name} - 异常: {e}")
        
        # 总结
        total_tests = len(scenarios)
        logging.info(f"\n📊 测试总结: {passed_count}/{total_tests} 个场景通过")
        
        if passed_count == total_tests:
            logging.info("🎉 所有AdEMAMix场景测试通过！")
        else:
            logging.warning(f"⚠️ {total_tests - passed_count} 个场景测试失败")
        
        self.test_results = results
        return results


def main():
    """主函数"""
    print("=" * 60)
    print("AdEMAMix优化器独立场景测试")
    print("基于原文结构的场景验证")
    print("=" * 60)
    
    # 创建测试器
    tester = AdEMAMixScenarioTester()
    
    # 运行所有测试
    results = tester.run_all_scenarios()
    
    # 输出详细结果
    print("\n" + "=" * 60)
    print("详细测试结果:")
    for scenario, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {scenario}: {status}")
    
    print("=" * 60)
    return all(results.values())


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
