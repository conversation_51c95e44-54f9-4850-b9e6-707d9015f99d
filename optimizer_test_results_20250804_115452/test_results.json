{"adam": {"optimizer_name": "adam", "final_train_loss": 0.0856967344880104, "final_val_loss": 0.06709998846054077, "convergence_epoch": 99, "convergence_speed": 75, "training_time_seconds": 70.06584978103638, "val_loss_stability": 0.0008584518036637211, "memory_delta_mb": 90.328125, "history": {"loss": [1.0025432109832764, 0.9223801493644714, 0.6113241314888, 0.30049124360084534, 0.24652479588985443, 0.22665229439735413, 0.2182726413011551, 0.2058582901954651, 0.19485726952552795, 0.19047600030899048, 0.18425898253917694, 0.18005825579166412, 0.17704935371875763, 0.17112179100513458, 0.16529370844364166, 0.16253948211669922, 0.1619817167520523, 0.16246002912521362, 0.15936732292175293, 0.1532880663871765, 0.15808293223381042, 0.15384440124034882, 0.14925634860992432, 0.15225936472415924, 0.1474785953760147, 0.14026378095149994, 0.14161469042301178, 0.14041994512081146, 0.13534298539161682, 0.13444994390010834, 0.13332055509090424, 0.13240884244441986, 0.1312023401260376, 0.1305067390203476, 0.13116954267024994, 0.12731093168258667, 0.12633925676345825, 0.12381551414728165, 0.12104779481887817, 0.12172115594148636, 0.11991392821073532, 0.12115924060344696, 0.11578306555747986, 0.11481915414333344, 0.11885327100753784, 0.1184016689658165, 0.11181903630495071, 0.10917210578918457, 0.11333907395601273, 0.11111800372600555, 0.10979591310024261, 0.10812731832265854, 0.10882057249546051, 0.10632309317588806, 0.10626992583274841, 0.10589659959077835, 0.10271880775690079, 0.10580167174339294, 0.10467540472745895, 0.1023349016904831, 0.10047751665115356, 0.10321757942438126, 0.10238341987133026, 0.10105770081281662, 0.09850720316171646, 0.1001053974032402, 0.10067031532526016, 0.09953750669956207, 0.09548263251781464, 0.09754998981952667, 0.09496480226516724, 0.09441852569580078, 0.09732717275619507, 0.09371811896562576, 0.09526762366294861, 0.09287797659635544, 0.09395188093185425, 0.09400162100791931, 0.092621810734272, 0.09180869907140732, 0.09325475990772247, 0.09249366074800491, 0.09101437777280807, 0.09232986718416214, 0.08859752863645554, 0.09081543982028961, 0.09253328293561935, 0.0908946692943573, 0.09262221306562424, 0.09018506854772568, 0.08835842460393906, 0.09092093259096146, 0.08868534862995148, 0.09039068967103958, 0.09094417840242386, 0.0888763964176178, 0.08951173722743988, 0.0879567563533783, 0.0856967344880104, 0.0884220227599144], "val_loss": [0.9094696640968323, 0.758821427822113, 0.30488038063049316, 0.2130289077758789, 0.20226487517356873, 0.18786455690860748, 0.18971195816993713, 0.18322725594043732, 0.1734403669834137, 0.17544330656528473, 0.16869403421878815, 0.1726991981267929, 0.17148138582706451, 0.1711615025997162, 0.16114316880702972, 0.163755401968956, 0.15447428822517395, 0.15527692437171936, 0.15201060473918915, 0.1494835615158081, 0.14671847224235535, 0.14221420884132385, 0.13740567862987518, 0.13809192180633545, 0.1357087790966034, 0.13597527146339417, 0.13341595232486725, 0.12764988839626312, 0.12173843383789062, 0.12217498570680618, 0.11835826188325882, 0.11851368099451065, 0.1233869269490242, 0.1149664893746376, 0.11093214154243469, 0.10853604972362518, 0.10901454091072083, 0.10141754150390625, 0.10604742914438248, 0.10229215770959854, 0.1010250672698021, 0.09973043203353882, 0.09703768789768219, 0.098603755235672, 0.09774740040302277, 0.09344065934419632, 0.09237777441740036, 0.09023281186819077, 0.08925483375787735, 0.08772748708724976, 0.08870559930801392, 0.08638644218444824, 0.08654959499835968, 0.08738717436790466, 0.08428741246461868, 0.08571510016918182, 0.08379421383142471, 0.08421190083026886, 0.08182649314403534, 0.08190812170505524, 0.08074229210615158, 0.08099771291017532, 0.08013039827346802, 0.07915890961885452, 0.07835685461759567, 0.08066381514072418, 0.07669367641210556, 0.07844292372465134, 0.07746310532093048, 0.07667367905378342, 0.07542173564434052, 0.0745960995554924, 0.07482695579528809, 0.07500027865171432, 0.07263164967298508, 0.07365477830171585, 0.07414696365594864, 0.07367177307605743, 0.07116474956274033, 0.07448498904705048, 0.07225336134433746, 0.07234718650579453, 0.07255669683218002, 0.07046395540237427, 0.07184582948684692, 0.0726417750120163, 0.07006673514842987, 0.06953120976686478, 0.06963925063610077, 0.0723799616098404, 0.06857062131166458, 0.06972060352563858, 0.06925859302282333, 0.0696670413017273, 0.06976820528507233, 0.06825021654367447, 0.0680820420384407, 0.06925266236066818, 0.06709998846054077, 0.06798193603754044]}, "start_resources": {"cpu_percent": 47.3, "memory_rss_mb": 411.96875, "memory_vms_mb": 34226.90234375, "memory_percent": 2.514481544494629, "gpu_memory_mb": 0.0}, "end_resources": {"cpu_percent": 58.9, "memory_rss_mb": 502.296875, "memory_vms_mb": 34319.015625, "memory_percent": 3.065776824951172, "gpu_memory_mb": 0.0}, "success": true}, "adamw": {"optimizer_name": "adamw", "final_train_loss": 0.08724597841501236, "final_val_loss": 0.06804194301366806, "convergence_epoch": 98, "convergence_speed": 69, "training_time_seconds": 69.74045968055725, "val_loss_stability": 0.0006293900307355013, "memory_delta_mb": 43.98046875, "history": {"loss": [1.0029017925262451, 0.9380303025245667, 0.656909167766571, 0.3114682137966156, 0.24830655753612518, 0.22168703377246857, 0.2143387347459793, 0.20367330312728882, 0.19538715481758118, 0.1857534795999527, 0.1845007687807083, 0.18178147077560425, 0.17607565224170685, 0.17331455647945404, 0.17348921298980713, 0.16834291815757751, 0.16233479976654053, 0.16645289957523346, 0.1590825766324997, 0.15897603332996368, 0.15464788675308228, 0.14877066016197205, 0.15247179567813873, 0.14937978982925415, 0.14448212087154388, 0.14680369198322296, 0.14114125072956085, 0.13817182183265686, 0.14069248735904694, 0.13497023284435272, 0.13649386167526245, 0.1335008293390274, 0.13083162903785706, 0.1304861456155777, 0.12906107306480408, 0.12759584188461304, 0.12492045760154724, 0.12229493260383606, 0.12341257929801941, 0.12180160731077194, 0.12227635085582733, 0.11962447315454483, 0.11923015862703323, 0.11718092858791351, 0.11326473951339722, 0.11283782124519348, 0.11291174590587616, 0.11001767218112946, 0.10979683697223663, 0.1088600903749466, 0.11091771721839905, 0.10773750394582748, 0.10813149809837341, 0.10476545244455338, 0.10553547739982605, 0.10516142100095749, 0.10409083962440491, 0.10208680480718613, 0.10286856442689896, 0.10240139067173004, 0.10026230663061142, 0.10080622881650925, 0.10053341835737228, 0.100216805934906, 0.09898094087839127, 0.09925402700901031, 0.09701915085315704, 0.09626096487045288, 0.09797389805316925, 0.09474661946296692, 0.09528632462024689, 0.09654088318347931, 0.094634510576725, 0.09613344073295593, 0.09564593434333801, 0.09341590851545334, 0.09429655224084854, 0.09403079748153687, 0.09341111779212952, 0.09183276444673538, 0.08863872289657593, 0.09070084244012833, 0.09226636588573456, 0.09235639870166779, 0.08838322758674622, 0.09044957160949707, 0.09134749323129654, 0.08985298126935959, 0.08831503987312317, 0.09050299972295761, 0.0896061360836029, 0.08858847618103027, 0.08889992535114288, 0.08811493963003159, 0.08869477361440659, 0.08817809820175171, 0.08754421025514603, 0.08893020451068878, 0.08910325169563293, 0.08724597841501236], "val_loss": [0.9165909290313721, 0.7958648800849915, 0.3568059802055359, 0.20773136615753174, 0.20027010142803192, 0.18320681154727936, 0.17892856895923615, 0.18242467939853668, 0.1667601615190506, 0.15962007641792297, 0.16574975848197937, 0.15948893129825592, 0.16277416050434113, 0.15673097968101501, 0.1557072401046753, 0.15345248579978943, 0.15405884385108948, 0.14199869334697723, 0.1413060426712036, 0.14460459351539612, 0.1450774073600769, 0.14072997868061066, 0.13804428279399872, 0.1365075558423996, 0.13527542352676392, 0.13357436656951904, 0.12822304666042328, 0.13041284680366516, 0.1260671466588974, 0.12630034983158112, 0.12085255980491638, 0.11810295283794403, 0.11656955629587173, 0.11702094972133636, 0.1137634739279747, 0.11395473778247833, 0.10829434543848038, 0.10782548040151596, 0.10371535271406174, 0.1040332093834877, 0.1005946472287178, 0.09889344871044159, 0.09775498509407043, 0.09811075031757355, 0.09870164841413498, 0.1006833016872406, 0.09370175004005432, 0.09201657027006149, 0.09408151358366013, 0.09144468605518341, 0.0895863026380539, 0.08758558332920074, 0.08639699965715408, 0.08750683069229126, 0.08782535791397095, 0.08443719148635864, 0.0824691653251648, 0.08209782838821411, 0.07947277277708054, 0.08216165751218796, 0.08200877159833908, 0.08250930160284042, 0.07955022156238556, 0.07657361030578613, 0.07513461261987686, 0.07745847851037979, 0.07814671099185944, 0.07638654112815857, 0.07451532781124115, 0.07451571524143219, 0.07572607696056366, 0.07265783101320267, 0.07478856295347214, 0.07459075003862381, 0.0741560086607933, 0.07246343046426773, 0.07112565636634827, 0.07011068612337112, 0.07276575267314911, 0.07097434252500534, 0.07209909707307816, 0.07129639387130737, 0.07125826179981232, 0.07021405547857285, 0.06985180824995041, 0.07019331306219101, 0.06961096078157425, 0.0696546658873558, 0.06919308751821518, 0.06963372975587845, 0.06950108706951141, 0.06986574828624725, 0.0682772621512413, 0.0691656842827797, 0.07006296515464783, 0.06953060626983643, 0.06900479644536972, 0.06804194301366806, 0.06869048625230789, 0.06874620169401169]}, "start_resources": {"cpu_percent": 22.3, "memory_rss_mb": 490.85546875, "memory_vms_mb": 34305.0703125, "memory_percent": 2.995944023132324, "gpu_memory_mb": 0.0}, "end_resources": {"cpu_percent": 58.8, "memory_rss_mb": 534.8359375, "memory_vms_mb": 34350.2109375, "memory_percent": 3.2643795013427734, "gpu_memory_mb": 0.0}, "success": true}, "lion": {"optimizer_name": "lion", "success": false, "error": "BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'", "training_time_seconds": 0.09072709083557129}, "ademamix": {"optimizer_name": "ademamix", "success": false, "error": "BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'", "training_time_seconds": 0.09647989273071289}, "fractional": {"optimizer_name": "fractional", "success": false, "error": "BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'", "training_time_seconds": 0.09337496757507324}, "comparison": {"rankings": {"final_val_loss": ["adam", "adamw"], "convergence_speed": ["adamw", "adam"], "training_time_seconds": ["adamw", "adam"], "val_loss_stability": ["adamw", "adam"], "memory_delta_mb": ["adamw", "adam"]}, "scores": {"adam": 6, "adamw": 9}, "best_optimizer": "adamw", "summary": {"total_tests": 5, "successful_tests": 2, "failed_tests": 3}}, "test_config": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "loss": "mse", "metrics": ["mae", "mse"], "test_name": "financial_time_series_optimizer_comparison"}, "total_test_time": 168.45202088356018}