#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小规模训练测试
在云服务器上进行实际的小规模训练，验证所有修复的有效性
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
import time
from datetime import datetime

# 设置日志
log_filename = f"mini_training_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)

def create_mock_data():
    """创建模拟数据进行测试"""
    logging.info("🔧 创建模拟数据...")
    
    try:
        # 创建模拟股票数据
        dates = pd.date_range('2024-01-01', '2024-12-31', freq='D')
        stock_codes = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
        
        data = []
        for stock_code in stock_codes:
            for date in dates:
                # 模拟基础数据
                row = {
                    'ts_code': stock_code,
                    'trade_date': date.strftime('%Y%m%d'),
                    'open': np.random.uniform(10, 50),
                    'high': np.random.uniform(10, 60),
                    'low': np.random.uniform(5, 45),
                    'close': np.random.uniform(10, 55),
                    'vol': np.random.uniform(1000, 100000),
                    'amount': np.random.uniform(10000, 1000000),
                    'pct_chg': np.random.uniform(-10, 10),
                    'turnover_rate': np.random.uniform(0.1, 15),
                    'pe': np.random.uniform(5, 100),
                    'pb': np.random.uniform(0.5, 10),
                    'total_mv': np.random.uniform(1000000, 10000000),
                    'circ_mv': np.random.uniform(800000, 8000000),
                }
                
                # 添加技术指标
                for i in range(5, 21):  # MA5到MA20
                    row[f'ma_{i}'] = np.random.uniform(10, 50)
                
                # 添加其他技术指标
                row['rsi'] = np.random.uniform(20, 80)
                row['macd'] = np.random.uniform(-2, 2)
                row['kdj_k'] = np.random.uniform(0, 100)
                row['kdj_d'] = np.random.uniform(0, 100)
                row['kdj_j'] = np.random.uniform(0, 100)
                row['cci'] = np.random.uniform(-200, 200)
                row['boll_upper'] = np.random.uniform(15, 60)
                row['boll_mid'] = np.random.uniform(10, 50)
                row['boll_lower'] = np.random.uniform(5, 45)
                
                # 添加涨停标记
                row['is_limit_up'] = 1 if np.random.random() < 0.05 else 0
                row['consecutive_limit_days'] = np.random.randint(0, 5) if row['is_limit_up'] else 0
                
                data.append(row)
        
        df = pd.DataFrame(data)
        logging.info(f"✅ 创建模拟数据完成: {len(df)}条记录")
        return df
        
    except Exception as e:
        logging.error(f"❌ 创建模拟数据失败: {e}")
        return None

def test_mini_training():
    """进行小规模训练测试"""
    logging.info("🚀 开始小规模训练测试...")
    
    try:
        # 导入必要的模块
        import tensorflow as tf
        
        # 创建模拟数据
        df = create_mock_data()
        if df is None:
            return False
        
        # 准备训练数据
        logging.info("📊 准备训练数据...")
        
        # 选择特征列
        feature_cols = [col for col in df.columns if col not in ['ts_code', 'trade_date', 'is_limit_up', 'consecutive_limit_days']]
        X = df[feature_cols].fillna(0).values
        
        # 创建序列数据
        sequence_length = 3  # 超短线配置
        X_sequences = []
        y_classification = []
        y_regression = []
        
        for i in range(sequence_length, len(X)):
            X_sequences.append(X[i-sequence_length:i])
            y_classification.append(df.iloc[i]['is_limit_up'])
            y_regression.append(df.iloc[i]['pct_chg'] / 100.0)  # 归一化
        
        X_sequences = np.array(X_sequences)
        y_classification = np.array(y_classification)
        y_regression = np.array(y_regression)
        
        # 分割数据
        split_idx = int(len(X_sequences) * 0.8)
        X_train = X_sequences[:split_idx]
        X_test = X_sequences[split_idx:]
        y_train_cls = y_classification[:split_idx]
        y_test_cls = y_classification[split_idx:]
        y_train_reg = y_regression[:split_idx]
        y_test_reg = y_regression[split_idx:]
        
        logging.info(f"📈 训练数据形状: X_train{X_train.shape}, y_train_cls{y_train_cls.shape}")
        
        # 构建简单模型
        logging.info("🏗️ 构建模型...")
        
        input_layer = tf.keras.layers.Input(shape=(sequence_length, len(feature_cols)))
        lstm_layer = tf.keras.layers.LSTM(32, return_sequences=False)(input_layer)
        dropout_layer = tf.keras.layers.Dropout(0.2)(lstm_layer)
        
        # 分类输出
        cls_output = tf.keras.layers.Dense(1, activation='sigmoid', name='classification_output')(dropout_layer)
        
        # 回归输出
        reg_output = tf.keras.layers.Dense(1, activation='linear', name='regression_output')(dropout_layer)
        
        model = tf.keras.Model(inputs=input_layer, outputs=[cls_output, reg_output])
        
        # 编译模型（使用修复后的配置）
        logging.info("⚙️ 编译模型...")
        
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss={
                'classification_output': 'binary_crossentropy',
                'regression_output': 'mse'
            },
            loss_weights={
                'classification_output': 2.0,
                'regression_output': 1.0
            },
            metrics={
                'classification_output': [
                    tf.keras.metrics.BinaryAccuracy(name='binary_accuracy'),
                    tf.keras.metrics.AUC(name='auc')
                ],
                'regression_output': [
                    tf.keras.metrics.MeanSquaredError(name='mse')
                ]
            }
        )
        
        # 创建数据集（使用修复后的方法）
        logging.info("📦 创建数据集...")
        
        def create_simple_dataset(X, y_cls, y_reg, batch_size, shuffle=True):
            """简化数据集创建"""
            y_dict = {
                'classification_output': y_cls,
                'regression_output': y_reg
            }
            
            dataset = tf.data.Dataset.from_tensor_slices((X, y_dict))
            
            if shuffle:
                dataset = dataset.shuffle(buffer_size=min(len(X), 1000))
            
            dataset = dataset.batch(batch_size)
            dataset = dataset.prefetch(1)
            
            return dataset
        
        train_dataset = create_simple_dataset(X_train, y_train_cls, y_train_reg, 32, shuffle=True)
        val_dataset = create_simple_dataset(X_test, y_test_cls, y_test_reg, 32, shuffle=False)
        
        # 创建回调函数
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=3,
                restore_best_weights=True,
                verbose=1
            )
        ]
        
        # 开始训练（使用修复后的方法，无重试）
        logging.info("🚀 开始训练...")
        
        # 清理GPU内存
        if tf.config.list_physical_devices('GPU'):
            tf.keras.backend.clear_session()
        
        # 直接训练，无重试机制
        history = model.fit(
            train_dataset,
            validation_data=val_dataset,
            epochs=5,  # 小规模测试，只训练5个epoch
            callbacks=callbacks,
            verbose=1,
            workers=1,
            use_multiprocessing=False
        )
        
        logging.info("✅ 训练完成")
        
        # 评估模型
        logging.info("📊 评估模型...")
        
        results = model.evaluate(val_dataset, verbose=0)
        logging.info(f"✅ 评估结果: {results}")
        
        # 验证训练历史
        if history and history.history:
            epochs_completed = len(history.history['loss'])
            logging.info(f"✅ 完成{epochs_completed}个epoch的训练")
            
            # 检查AUC指标
            auc_keys = [k for k in history.history.keys() if 'auc' in k.lower()]
            if auc_keys:
                final_auc = history.history[auc_keys[0]][-1]
                logging.info(f"✅ 最终AUC: {final_auc:.4f}")
            else:
                logging.warning("⚠️ 未找到AUC指标")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 小规模训练测试失败: {e}")
        import traceback
        logging.error(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    start_time = time.time()
    logging.info("="*80)
    logging.info("🚀 小规模训练测试开始")
    logging.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_filename}")
    logging.info("="*80)
    
    try:
        # 检查TensorFlow
        import tensorflow as tf
        logging.info(f"✅ TensorFlow版本: {tf.__version__}")
        
        # 检查GPU
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            logging.info(f"✅ 发现GPU: {len(gpus)}个")
        else:
            logging.info("ℹ️ 使用CPU训练")
        
        # 执行训练测试
        success = test_mini_training()
        
        # 总结
        total_duration = time.time() - start_time
        logging.info(f"\n{'='*80}")
        logging.info("📊 小规模训练测试总结")
        logging.info(f"{'='*80}")
        logging.info(f"测试结果: {'✅ 成功' if success else '❌ 失败'}")
        logging.info(f"总耗时: {total_duration:.2f}秒")
        
        if success:
            logging.info("\n🎉 小规模训练测试成功！")
            logging.info("✅ 所有修复都有效，系统可以正常训练")
            print(f"\n🎉 小规模训练测试成功！日志文件: {log_filename}")
            return True
        else:
            logging.error("\n❌ 小规模训练测试失败")
            logging.error("需要进一步检查和修复")
            print(f"\n❌ 小规模训练测试失败，详见日志文件: {log_filename}")
            return False
        
    except ImportError as e:
        logging.error(f"❌ 导入错误: {e}")
        print(f"\n❌ 缺少必要的依赖: {e}")
        return False
    except Exception as e:
        logging.error(f"❌ 测试程序运行失败: {e}")
        print(f"\n❌ 测试程序异常: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logging.error(f"程序异常退出: {e}")
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
