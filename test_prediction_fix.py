#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测模块修复测试脚本
专门测试修复的预测功能，不运行完整的训练流程
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
import importlib.util
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'prediction_fix_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

def load_module_from_file(file_path, module_name):
    """从文件路径加载模块"""
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

def test_vectorized_get_market_type():
    """测试向量化市场类型获取函数"""
    logging.info("🔧 测试 vectorized_get_market_type 函数...")
    
    try:
        # 加载P.pull模块
        P_pull = load_module_from_file('/home/<USER>/P.pull.py', 'P_pull')
        
        # 测试数据
        test_codes = pd.Series([
            '000001.SZ',  # 深圳主板
            '600000.SH',  # 上海主板
            '300001.SZ',  # 创业板
            '688001.SH',  # 科创板
            '430001.BJ'   # 北交所
        ])
        
        # 调用函数
        result = P_pull.vectorized_get_market_type(test_codes)
        
        # 验证结果
        logging.info(f"✅ 函数返回类型: {type(result)}")
        logging.info(f"✅ 返回结果: {result}")
        
        # 验证是否为numpy数组
        if isinstance(result, np.ndarray):
            logging.info("✅ 返回类型正确：numpy.ndarray")
        else:
            logging.error(f"❌ 返回类型错误：期望numpy.ndarray，实际{type(result)}")
            return False
            
        # 验证结果内容
        expected = ['MAIN', 'MAIN', 'CHINEXT', 'STAR', 'BSE']
        if list(result) == expected:
            logging.info("✅ 返回结果正确")
        else:
            logging.error(f"❌ 返回结果错误：期望{expected}，实际{list(result)}")
            return False
            
        return True
        
    except Exception as e:
        logging.error(f"❌ vectorized_get_market_type测试失败: {str(e)}")
        return False

def test_market_type_processing():
    """测试市场类型处理逻辑"""
    logging.info("🔧 测试市场类型处理逻辑...")
    
    try:
        # 模拟预测过程中的市场类型处理
        ts_codes = ['000001.SZ', '600000.SH', '300001.SZ', '688001.SH', '430001.BJ']
        ts_codes_series = pd.Series(ts_codes)
        
        # 加载P.pull模块
        P_pull = load_module_from_file('/home/<USER>/P.pull.py', 'P_pull')
        
        # 测试修复后的代码逻辑
        market_types = P_pull.vectorized_get_market_type(ts_codes_series)
        
        # 验证不会出现.values错误
        logging.info(f"✅ 市场类型获取成功: {market_types}")
        
        # 测试后续的向量化操作
        main_mask = market_types == 'MAIN'
        chinext_mask = market_types == 'CHINEXT'
        star_mask = market_types == 'STAR'
        bse_mask = market_types == 'BSE'
        
        logging.info(f"✅ MAIN掩码: {main_mask}")
        logging.info(f"✅ CHINEXT掩码: {chinext_mask}")
        logging.info(f"✅ STAR掩码: {star_mask}")
        logging.info(f"✅ BSE掩码: {bse_mask}")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 市场类型处理测试失败: {str(e)}")
        return False

def test_prediction_data_flow():
    """测试预测数据流处理"""
    logging.info("🔧 测试预测数据流处理...")
    
    try:
        # 模拟预测结果数据
        predictions = [
            np.random.rand(5, 1),  # 次日涨停概率
            np.random.rand(5, 1),  # 次日涨幅
            np.random.rand(5, 1),  # 后日涨停概率
            np.random.rand(5, 1)   # 后日涨幅
        ]
        
        ts_codes = ['000001.SZ', '600000.SH', '300001.SZ', '688001.SH', '430001.BJ']
        strategy_type = '首板'
        
        # 加载P.pull模块
        P_pull = load_module_from_file('/home/<USER>/P.pull.py', 'P_pull')
        
        # 测试市场类型获取（修复后的版本）
        ts_codes_series = pd.Series(ts_codes)
        market_types = P_pull.vectorized_get_market_type(ts_codes_series)
        
        logging.info(f"✅ 市场类型获取成功，无.values错误")
        
        # 模拟反标准化参数设置
        DEFAULT_NORMALIZATION_PARAMS = {
            '首板': {
                'MAIN': {
                    'regression_output_1': {'median': 1.0, 'iqr': 8.0},
                    'regression_output_2': {'median': 1.0, 'iqr': 8.0}
                },
                'CHINEXT': {
                    'regression_output_1': {'median': 1.0, 'iqr': 8.0},
                    'regression_output_2': {'median': 1.0, 'iqr': 8.0}
                }
            }
        }
        
        # 测试向量化参数获取
        next_medians = np.zeros(len(ts_codes))
        next_iqrs = np.zeros(len(ts_codes))
        second_medians = np.zeros(len(ts_codes))
        second_iqrs = np.zeros(len(ts_codes))
        
        if strategy_type in DEFAULT_NORMALIZATION_PARAMS:
            param_map = DEFAULT_NORMALIZATION_PARAMS[strategy_type]
            
            main_mask = market_types == 'MAIN'
            chinext_mask = market_types == 'CHINEXT'
            
            if np.any(main_mask) and 'MAIN' in param_map:
                params = param_map['MAIN']
                next_medians[main_mask] = params['regression_output_1']['median']
                next_iqrs[main_mask] = params['regression_output_1']['iqr']
                
        logging.info(f"✅ 向量化参数设置成功")
        logging.info(f"✅ next_medians: {next_medians}")
        logging.info(f"✅ next_iqrs: {next_iqrs}")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 预测数据流测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logging.info("=" * 60)
    logging.info("🚀 预测模块修复测试开始")
    logging.info(f"测试时间: {datetime.now()}")
    logging.info("=" * 60)
    
    tests = [
        ("vectorized_get_market_type函数", test_vectorized_get_market_type),
        ("市场类型处理逻辑", test_market_type_processing),
        ("预测数据流处理", test_prediction_data_flow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logging.info(f"\n{'='*50}")
        logging.info(f"🔧 运行测试: {test_name}")
        logging.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logging.info(f"✅ {test_name} - 通过")
            else:
                logging.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logging.error(f"❌ {test_name} - 异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试总结
    logging.info(f"\n{'='*60}")
    logging.info("📊 预测模块修复测试总结")
    logging.info(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"{test_name}: {status}")
    
    logging.info(f"\n📈 测试统计:")
    logging.info(f"  总测试数: {total}")
    logging.info(f"  通过数: {passed}")
    logging.info(f"  失败数: {total - passed}")
    logging.info(f"  成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        logging.info(f"\n🎉 所有预测模块修复测试通过！")
        return True
    else:
        logging.error(f"\n⚠️ 有 {total - passed} 个测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
