金融时间序列优化器测试报告
==================================================

测试时间: 2025-08-04 12:22:12

推荐结果:
风险调整后最佳优化器: lion
效率最佳优化器: fractional
总结: 风险调整后最佳: lion, 效率最佳: fractional

详细性能数据:
------------------------------

adam:
  最终验证损失: 0.067592
  收敛速度: 71 epochs
  训练时间: 38.37 秒
  稳定性: 0.000699
  内存使用: 251.06 MB

adamw:
  最终验证损失: 0.071302
  收敛速度: 64 epochs
  训练时间: 40.16 秒
  稳定性: 0.000524
  内存使用: 65.96 MB

lion:
  最终验证损失: 0.068327
  收敛速度: 80 epochs
  训练时间: 40.48 秒
  稳定性: 0.001282
  内存使用: 36.43 MB

ademamix:
  最终验证损失: 0.070995
  收敛速度: 69 epochs
  训练时间: 40.44 秒
  稳定性: 0.000475
  内存使用: 35.96 MB

fractional:
  最终验证损失: 0.067087
  收敛速度: 80 epochs
  训练时间: 40.22 秒
  稳定性: 0.000921
  内存使用: 34.91 MB
