#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码清理验证测试
验证P.pull.py中的代码清理效果：
1. 重复函数删除
2. 未使用函数删除
3. 向量化操作应用
4. 代码质量提升
"""

import sys
import os
import re
import logging
from datetime import datetime

# 设置日志
log_filename = f"code_cleanup_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)

def read_file_content(file_path):
    """读取文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logging.error(f"读取文件失败: {e}")
        return None

def test_duplicate_functions_removed():
    """测试重复函数是否已删除"""
    logging.info("🔧 测试重复函数删除...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    # 检查重复的首板函数
    enhanced_shouban_count = len(re.findall(r'def generate_enhanced_shouban_samples\(', content))
    comprehensive_shouban_count = len(re.findall(r'def generate_comprehensive_shouban_samples\(', content))
    unified_shouban_count = len(re.findall(r'def generate_unified_shouban_samples\(', content))
    
    # 检查重复的连板函数
    enhanced_lianban_count = len(re.findall(r'def generate_enhanced_lianban_samples\(', content))
    comprehensive_lianban_count = len(re.findall(r'def generate_comprehensive_lianban_samples\(', content))
    unified_lianban_count = len(re.findall(r'def generate_unified_lianban_samples\(', content))
    
    results = []
    
    # 验证首板函数
    if enhanced_shouban_count == 0 and comprehensive_shouban_count == 0 and unified_shouban_count == 1:
        logging.info("✅ 首板样本生成函数已成功合并")
        results.append(True)
    else:
        logging.error(f"❌ 首板函数清理失败: enhanced={enhanced_shouban_count}, comprehensive={comprehensive_shouban_count}, unified={unified_shouban_count}")
        results.append(False)
    
    # 验证连板函数
    if enhanced_lianban_count == 0 and comprehensive_lianban_count == 0 and unified_lianban_count == 1:
        logging.info("✅ 连板样本生成函数已成功合并")
        results.append(True)
    else:
        logging.error(f"❌ 连板函数清理失败: enhanced={enhanced_lianban_count}, comprehensive={comprehensive_lianban_count}, unified={unified_lianban_count}")
        results.append(False)
    
    # 检查函数调用是否已更新
    old_calls = len(re.findall(r'generate_enhanced_shouban_samples\(|generate_comprehensive_shouban_samples\(|generate_enhanced_lianban_samples\(|generate_comprehensive_lianban_samples\(', content))
    new_calls = len(re.findall(r'generate_unified_shouban_samples\(|generate_unified_lianban_samples\(', content))
    
    if old_calls == 0 and new_calls >= 2:
        logging.info("✅ 函数调用已成功更新为统一函数")
        results.append(True)
    else:
        logging.error(f"❌ 函数调用更新失败: old_calls={old_calls}, new_calls={new_calls}")
        results.append(False)
    
    return all(results)

def test_unused_functions_removed():
    """测试未使用函数是否已删除"""
    logging.info("🔧 测试未使用函数删除...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    # 检查已删除的函数
    process_stock_batch_def = len(re.findall(r'def process_stock_batch\(', content))
    preprocess_stock_data_def = len(re.findall(r'def preprocess_stock_data\(', content))
    
    # 检查函数调用
    process_stock_batch_call = len(re.findall(r'process_stock_batch\(', content)) - process_stock_batch_def
    preprocess_stock_data_call = len(re.findall(r'preprocess_stock_data\(', content)) - preprocess_stock_data_def
    
    results = []
    
    # 验证process_stock_batch删除
    if process_stock_batch_def == 0:
        logging.info("✅ process_stock_batch函数已成功删除")
        results.append(True)
    else:
        logging.error(f"❌ process_stock_batch函数未删除: def_count={process_stock_batch_def}")
        results.append(False)
    
    # 验证preprocess_stock_data删除
    if preprocess_stock_data_def == 0:
        logging.info("✅ preprocess_stock_data函数已成功删除")
        results.append(True)
    else:
        logging.error(f"❌ preprocess_stock_data函数未删除: def_count={preprocess_stock_data_def}")
        results.append(False)
    
    return all(results)

def test_vectorization_applied():
    """测试向量化操作是否已应用"""
    logging.info("🔧 测试向量化操作应用...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    # 检查向量化操作的关键词
    vectorization_patterns = [
        r'np\.select\(',           # numpy select操作
        r'np\.where\(',            # numpy where操作
        r'\.apply\(',              # pandas apply操作
        r'vectorized_get_market_type',  # 向量化市场类型获取
        r'\.values',               # 获取numpy数组
        r'np\.sum\(',              # numpy求和
        r'np\.cumsum\(',           # numpy累计求和
    ]
    
    vectorization_counts = {}
    for pattern in vectorization_patterns:
        count = len(re.findall(pattern, content))
        vectorization_counts[pattern] = count
    
    results = []
    
    # 验证关键向量化操作
    np_select_pattern = r'np\.select\('
    if vectorization_counts[np_select_pattern] >= 2:
        logging.info(f"✅ np.select向量化操作已应用: {vectorization_counts[np_select_pattern]}次")
        results.append(True)
    else:
        logging.warning(f"⚠️ np.select使用较少: {vectorization_counts[np_select_pattern]}次")
        results.append(False)
    
    if vectorization_counts[r'vectorized_get_market_type'] >= 1:
        logging.info("✅ 向量化市场类型获取已应用")
        results.append(True)
    else:
        logging.error("❌ 向量化市场类型获取未应用")
        results.append(False)
    
    values_pattern = r'\.values'
    if vectorization_counts[values_pattern] >= 10:
        logging.info(f"✅ numpy数组操作已广泛应用: {vectorization_counts[values_pattern]}次")
        results.append(True)
    else:
        logging.warning(f"⚠️ numpy数组操作使用较少: {vectorization_counts[values_pattern]}次")
        results.append(False)
    
    # 检查低效操作是否减少
    inefficient_patterns = [
        r'\.iterrows\(\)',         # 低效的行迭代
        r'for.*in.*\.iterrows\(\)', # for循环中的iterrows
    ]
    
    for pattern in inefficient_patterns:
        count = len(re.findall(pattern, content))
        if count == 0:
            logging.info(f"✅ 低效操作已消除: {pattern}")
            results.append(True)
        else:
            logging.warning(f"⚠️ 仍存在低效操作: {pattern} ({count}次)")
            results.append(False)
    
    return all(results)

def test_code_quality_improvements():
    """测试代码质量提升"""
    logging.info("🔧 测试代码质量提升...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    # 统计代码行数和函数数量
    total_lines = len(content.split('\n'))
    function_count = len(re.findall(r'^def ', content, re.MULTILINE))
    comment_lines = len(re.findall(r'^\s*#', content, re.MULTILINE))
    
    # 检查修复标记
    fix_markers = len(re.findall(r'🔧 修复：', content))
    cleanup_markers = len(re.findall(r'删除重复|删除未被调用|删除无效', content))
    
    results = []
    
    logging.info(f"代码统计:")
    logging.info(f"  总行数: {total_lines}")
    logging.info(f"  函数数量: {function_count}")
    logging.info(f"  注释行数: {comment_lines}")
    logging.info(f"  修复标记: {fix_markers}")
    logging.info(f"  清理标记: {cleanup_markers}")
    
    # 验证清理标记
    if cleanup_markers >= 4:  # 至少4个清理标记
        logging.info("✅ 代码清理标记充足")
        results.append(True)
    else:
        logging.warning(f"⚠️ 代码清理标记不足: {cleanup_markers}")
        results.append(False)
    
    # 验证修复标记
    if fix_markers >= 10:  # 至少10个修复标记
        logging.info("✅ 代码修复标记充足")
        results.append(True)
    else:
        logging.warning(f"⚠️ 代码修复标记不足: {fix_markers}")
        results.append(False)
    
    # 检查函数平均长度（简单估算）
    avg_function_length = total_lines / max(function_count, 1)
    if avg_function_length < 100:  # 平均函数长度小于100行
        logging.info(f"✅ 函数平均长度合理: {avg_function_length:.1f}行")
        results.append(True)
    else:
        logging.warning(f"⚠️ 函数平均长度较长: {avg_function_length:.1f}行")
        results.append(False)
    
    return all(results)

def main():
    """主函数"""
    logging.info("="*80)
    logging.info("🚀 代码清理验证测试开始")
    logging.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_filename}")
    logging.info("="*80)
    
    tests = [
        ("重复函数删除", test_duplicate_functions_removed),
        ("未使用函数删除", test_unused_functions_removed),
        ("向量化操作应用", test_vectorization_applied),
        ("代码质量提升", test_code_quality_improvements)
    ]
    
    results = []
    for test_name, test_func in tests:
        logging.info(f"\n{'='*60}")
        logging.info(f"🔧 运行测试: {test_name}")
        logging.info(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logging.info(f"✅ {test_name} - 通过")
            else:
                logging.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logging.error(f"❌ {test_name} - 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结报告
    logging.info(f"\n{'='*80}")
    logging.info("📊 代码清理验证测试总结")
    logging.info(f"{'='*80}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"{test_name}: {status}")
    
    logging.info(f"\n📈 测试统计:")
    logging.info(f"  总测试数: {total}")
    logging.info(f"  通过数: {passed}")
    logging.info(f"  失败数: {total - passed}")
    logging.info(f"  成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        logging.info("\n🎉 所有代码清理验证测试通过！")
        logging.info("✅ 重复函数已删除")
        logging.info("✅ 未使用函数已删除")
        logging.info("✅ 向量化操作已应用")
        logging.info("✅ 代码质量已提升")
        print(f"\n🎉 代码清理验证完全通过！日志文件: {log_filename}")
        return True
    else:
        logging.error(f"\n⚠️ {total - passed} 个测试失败，需要进一步清理")
        print(f"\n⚠️ 部分清理验证失败，详见日志文件: {log_filename}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logging.error(f"测试程序运行失败: {e}")
        print(f"\n❌ 测试程序异常: {e}")
        sys.exit(1)
