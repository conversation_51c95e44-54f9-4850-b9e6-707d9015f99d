{"adam": {"optimizer_name": "adam", "final_train_loss": 0.09157924354076385, "final_val_loss": 0.0738050788640976, "convergence_epoch": 76, "convergence_speed": 66, "training_time_seconds": 65.6084361076355, "val_loss_stability": 0.0010655545837995124, "memory_delta_mb": 88.69140625, "history": {"loss": [1.0050350427627563, 0.9402707815170288, 0.6738846302032471, 0.3297160267829895, 0.24904413521289825, 0.23185686767101288, 0.2234928160905838, 0.21248401701450348, 0.20244432985782623, 0.202400803565979, 0.19307631254196167, 0.19186191260814667, 0.1819874346256256, 0.17838217318058014, 0.1750619113445282, 0.178472101688385, 0.16672223806381226, 0.16542154550552368, 0.16493870317935944, 0.16271160542964935, 0.1600174456834793, 0.15835332870483398, 0.1550285667181015, 0.15355320274829865, 0.1555214375257492, 0.14853091537952423, 0.1494942605495453, 0.1475941240787506, 0.14212898910045624, 0.14406700432300568, 0.13840146362781525, 0.1414639800786972, 0.1380460113286972, 0.1337907463312149, 0.13400393724441528, 0.13340958952903748, 0.1322367638349533, 0.13242235779762268, 0.12675829231739044, 0.12311524152755737, 0.12511426210403442, 0.12436141073703766, 0.12239998579025269, 0.12041221559047699, 0.11946546286344528, 0.11865556985139847, 0.11554098129272461, 0.11405104398727417, 0.11460775136947632, 0.11129464209079742, 0.11142025887966156, 0.1106351912021637, 0.10875606536865234, 0.11100918054580688, 0.10719988495111465, 0.10523958504199982, 0.10676274448633194, 0.10474403202533722, 0.10388114303350449, 0.10408435761928558, 0.10269246250391006, 0.10207060724496841, 0.10121935606002808, 0.09940215945243835, 0.10026006400585175, 0.10103307664394379, 0.09921416640281677, 0.09964695572853088, 0.09613505005836487, 0.09620819985866547, 0.0968499481678009, 0.09367268532514572, 0.09633772820234299, 0.09639117866754532, 0.09570155292749405, 0.0922752395272255, 0.09590017050504684, 0.09454600512981415, 0.09350673854351044, 0.09258130937814713, 0.09362074732780457, 0.09157924354076385, 0.09391932189464569, 0.09458497911691666, 0.09514502435922623, 0.09224558621644974], "val_loss": [0.9151991009712219, 0.7910907864570618, 0.37281501293182373, 0.21858079731464386, 0.20363807678222656, 0.19342544674873352, 0.1900860071182251, 0.18683339655399323, 0.1753745675086975, 0.17528434097766876, 0.178324356675148, 0.1780916452407837, 0.16900582611560822, 0.1656746119260788, 0.1733420193195343, 0.16387052834033966, 0.16573818027973175, 0.16361823678016663, 0.15463793277740479, 0.1548452079296112, 0.1533365696668625, 0.15042302012443542, 0.14426614344120026, 0.15074987709522247, 0.14515425264835358, 0.13810354471206665, 0.138687863945961, 0.13689108192920685, 0.1339172124862671, 0.12981006503105164, 0.1271795928478241, 0.12876039743423462, 0.12278106063604355, 0.11840537935495377, 0.119660884141922, 0.11649822443723679, 0.11748799681663513, 0.11397888511419296, 0.11087733507156372, 0.10760324448347092, 0.1081409826874733, 0.10390080511569977, 0.10539677739143372, 0.10358009487390518, 0.10221627354621887, 0.1004592627286911, 0.09779433161020279, 0.09758561849594116, 0.09525637328624725, 0.09233025461435318, 0.09200545400381088, 0.09138304740190506, 0.08974000066518784, 0.09365522116422653, 0.08954497426748276, 0.08780594915151596, 0.08501393347978592, 0.0893869698047638, 0.08609503507614136, 0.08611144870519638, 0.08453962206840515, 0.08315543830394745, 0.08353208750486374, 0.08332139253616333, 0.08265573531389236, 0.08052089065313339, 0.08171708136796951, 0.07969507575035095, 0.08025078475475311, 0.07915133237838745, 0.0796736553311348, 0.0771842822432518, 0.07714361697435379, 0.07687188684940338, 0.07753848284482956, 0.0738050788640976, 0.0760040432214737, 0.07726648449897766, 0.07624020427465439, 0.07490887492895126, 0.07418632507324219, 0.07425732165575027, 0.07504092156887054, 0.07398393005132675, 0.07470902800559998, 0.07383845001459122]}, "start_resources": {"cpu_percent": 44.7, "memory_rss_mb": 413.12890625, "memory_vms_mb": 34237.07421875, "memory_percent": 2.5215625762939453, "gpu_memory_mb": 0.0}, "end_resources": {"cpu_percent": 60.4, "memory_rss_mb": 501.8203125, "memory_vms_mb": 34320.30859375, "memory_percent": 3.062868118286133, "gpu_memory_mb": 0.0}, "success": true}, "adamw": {"optimizer_name": "adamw", "final_train_loss": 0.08807115256786346, "final_val_loss": 0.06867939233779907, "convergence_epoch": 100, "convergence_speed": 73, "training_time_seconds": 76.40488719940186, "val_loss_stability": 0.0004946932950493971, "memory_delta_mb": 44.328125, "history": {"loss": [0.9925724267959595, 0.8835005760192871, 0.5257589221000671, 0.25822028517723083, 0.23417150974273682, 0.22404468059539795, 0.20751342177391052, 0.19972051680088043, 0.1966020166873932, 0.18638794124126434, 0.1870662271976471, 0.18208269774913788, 0.1763385534286499, 0.17003828287124634, 0.16862478852272034, 0.1681077778339386, 0.16496537625789642, 0.16028085350990295, 0.15959611535072327, 0.15887564420700073, 0.15307481586933136, 0.1531563550233841, 0.14999578893184662, 0.14787662029266357, 0.1464289128780365, 0.14422541856765747, 0.1443733423948288, 0.14337307214736938, 0.14009834825992584, 0.13633202016353607, 0.13578152656555176, 0.13584402203559875, 0.13179826736450195, 0.12939921021461487, 0.1281229555606842, 0.12719149887561798, 0.12499522417783737, 0.12255162000656128, 0.11951938271522522, 0.12100617587566376, 0.1206025630235672, 0.11781955510377884, 0.11794377118349075, 0.11549454927444458, 0.11666271835565567, 0.11630215495824814, 0.11463261395692825, 0.11485827714204788, 0.11220576614141464, 0.10998528450727463, 0.10941503196954727, 0.10661657154560089, 0.10918105393648148, 0.10782677680253983, 0.10507052391767502, 0.1055665910243988, 0.10251698642969131, 0.10380933433771133, 0.10280093550682068, 0.10213858634233475, 0.10246674716472626, 0.100969098508358, 0.10265970230102539, 0.09809967875480652, 0.10020259767770767, 0.09873856604099274, 0.10029134899377823, 0.09972602128982544, 0.09738615155220032, 0.09514730423688889, 0.09405988454818726, 0.09520664066076279, 0.09292026609182358, 0.09516143798828125, 0.09473063796758652, 0.09485644102096558, 0.09303896874189377, 0.09371886402368546, 0.09352097660303116, 0.09377774596214294, 0.09121964126825333, 0.09282870590686798, 0.09394600242376328, 0.09053611010313034, 0.0920652374625206, 0.09062487632036209, 0.09009144455194473, 0.09081243723630905, 0.09183640778064728, 0.090104840695858, 0.08871893584728241, 0.0907372459769249, 0.08807115256786346, 0.09052055329084396, 0.0905345156788826, 0.09138473868370056, 0.08810833096504211, 0.08945807814598083, 0.08904634416103363, 0.08889705687761307], "val_loss": [0.8948726654052734, 0.6989111304283142, 0.27235695719718933, 0.22574204206466675, 0.19527119398117065, 0.19591856002807617, 0.18727274239063263, 0.17788048088550568, 0.18572454154491425, 0.17902572453022003, 0.1745324283838272, 0.17545081675052643, 0.16675212979316711, 0.16662609577178955, 0.15605400502681732, 0.1573551893234253, 0.15825000405311584, 0.15732623636722565, 0.15473312139511108, 0.1509094089269638, 0.1530202478170395, 0.1494297832250595, 0.14334547519683838, 0.1428900957107544, 0.14140775799751282, 0.13902506232261658, 0.1360069066286087, 0.13461291790008545, 0.12969177961349487, 0.13083405792713165, 0.12421195954084396, 0.12725183367729187, 0.12148639559745789, 0.11964741349220276, 0.11689306050539017, 0.11455854773521423, 0.11539711058139801, 0.11368989199399948, 0.11126825213432312, 0.10717300325632095, 0.10735800117254257, 0.10378602892160416, 0.10593724250793457, 0.1002340018749237, 0.10068078339099884, 0.09836681187152863, 0.09680825471878052, 0.09360022842884064, 0.09495220333337784, 0.09216441214084625, 0.09072992950677872, 0.09110942482948303, 0.08805887401103973, 0.08628971874713898, 0.08745989203453064, 0.08691222220659256, 0.08713728189468384, 0.08512360602617264, 0.08401475846767426, 0.08462537080049515, 0.08405667543411255, 0.07938263565301895, 0.08037788420915604, 0.08143951743841171, 0.07881361246109009, 0.08023326843976974, 0.07927662134170532, 0.08099287748336792, 0.07928416877985, 0.07780527323484421, 0.07564008980989456, 0.07635391503572464, 0.0737052783370018, 0.07640602439641953, 0.07433822751045227, 0.0756654441356659, 0.07492580264806747, 0.07271960377693176, 0.0720549076795578, 0.07180532813072205, 0.07135700434446335, 0.07246369123458862, 0.07084327936172485, 0.07300398498773575, 0.07046303153038025, 0.07055093348026276, 0.07140186429023743, 0.07213263213634491, 0.07126376032829285, 0.07117178291082382, 0.07050525397062302, 0.069786436855793, 0.07013973593711853, 0.07010169327259064, 0.06924619525671005, 0.069521464407444, 0.06990697979927063, 0.06947267800569534, 0.0699712485074997, 0.06867939233779907]}, "start_resources": {"cpu_percent": 19.2, "memory_rss_mb": 491.60546875, "memory_vms_mb": 34307.16015625, "memory_percent": 3.000521659851074, "gpu_memory_mb": 0.0}, "end_resources": {"cpu_percent": 59.3, "memory_rss_mb": 535.93359375, "memory_vms_mb": 34352.41796875, "memory_percent": 3.2710790634155273, "gpu_memory_mb": 0.0}, "success": true}, "lion": {"optimizer_name": "lion", "success": false, "error": "BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'", "training_time_seconds": 0.0926511287689209}, "ademamix": {"optimizer_name": "ademamix", "success": false, "error": "BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'", "training_time_seconds": 0.09218811988830566}, "fractional": {"optimizer_name": "fractional", "success": false, "error": "BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'", "training_time_seconds": 0.09683609008789062}, "comparison": {"rankings": {"final_val_loss": ["adamw", "adam"], "convergence_speed": ["adam", "adamw"], "training_time_seconds": ["adam", "adamw"], "val_loss_stability": ["adamw", "adam"], "memory_delta_mb": ["adamw", "adam"]}, "scores": {"adam": 7, "adamw": 8}, "best_optimizer": "adamw", "summary": {"total_tests": 5, "successful_tests": 2, "failed_tests": 3}}, "test_config": {"learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "loss": "mse", "metrics": ["mae", "mse"], "test_name": "financial_time_series_optimizer_comparison"}, "total_test_time": 170.61547803878784}