#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于原文结构场景的修复验证测试
模拟真实的训练和预测流程，验证所有修复是否正确
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
import importlib.util
from datetime import datetime
import tempfile
import tensorflow as tf
from unittest.mock import Mock, patch

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'scenario_fixes_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

def load_module_from_file(file_path, module_name):
    """从文件路径加载模块"""
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

def create_mock_data():
    """创建模拟数据"""
    # 模拟股票数据
    stock_data = pd.DataFrame({
        'ts_code': ['000001.SZ', '600000.SH', '300001.SZ', '688001.SH', '430001.BJ'] * 20,
        'trade_date': ['20250101'] * 100,
        'close': np.random.rand(100) * 100,
        'pct_chg': np.random.randn(100) * 5,
        'turnover_rate': np.random.rand(100) * 10,
        'market_code': ['SZ', 'SH', 'SZ', 'SH', 'BJ'] * 20
    })
    
    # 模拟特征数据
    X_train = np.random.rand(100, 3, 163)  # (样本数, 时间步, 特征数)
    y_train = [np.random.rand(100, 1), np.random.rand(100, 1)]  # 多输出
    
    X_val = np.random.rand(20, 3, 163)
    y_val = [np.random.rand(20, 1), np.random.rand(20, 1)]
    
    return stock_data, X_train, y_train, X_val, y_val

def test_scenario_1_dataset_creation():
    """场景1: 数据集创建 - 测试time变量和数据集名称冲突修复"""
    logging.info("🔧 场景1: 测试数据集创建场景...")
    
    try:
        # 模拟trial对象和数据
        class MockTrial:
            def __init__(self, number):
                self.number = number
        
        stock_data, X_train, y_train, X_val, y_val = create_mock_data()
        
        # 测试多个trial的数据集创建（模拟原文第5776-5778行的逻辑）
        trials = [MockTrial(i) for i in range(1, 4)]
        trial_ids = []
        
        for trial in trials:
            # 🔧 修复后的代码：使用time_module而不是time
            import time as time_module
            trial_unique_id = f"{trial.number}_{int(time_module.time() * 1000) % 10000}"
            trial_ids.append(trial_unique_id)
            
            logging.info(f"✅ Trial {trial.number} 唯一ID生成: {trial_unique_id}")
            
            # 模拟数据集创建
            batch_size = 32
            # 这里模拟create_trial_dataset的调用
            logging.info(f"✅ Trial {trial.number} 数据集创建成功，batch_size={batch_size}")
        
        # 验证所有trial_id都是唯一的
        if len(set(trial_ids)) == len(trial_ids):
            logging.info("✅ 所有trial ID都是唯一的，避免了数据集名称冲突")
            return True
        else:
            logging.error("❌ 发现重复的trial ID，可能导致数据集名称冲突")
            return False
            
    except Exception as e:
        logging.error(f"❌ 场景1测试失败: {str(e)}")
        return False

def test_scenario_2_model_training():
    """场景2: 模型训练 - 测试optimizer定义修复"""
    logging.info("🔧 场景2: 测试模型训练场景...")
    
    try:
        # 模拟Config和参数
        class MockConfig:
            USE_ADEMAMIX = False
            DEFAULT_LEARNING_RATE = 0.001
            GRADIENT_CLIP_NORM = 1.0
        
        # 模拟best_params（可能为None的情况）
        test_cases = [
            {"best_params": {"learning_rate": 0.01}, "desc": "有最佳参数"},
            {"best_params": None, "desc": "无最佳参数"},
            {"best_params": {}, "desc": "空参数字典"}
        ]
        
        for case in test_cases:
            logging.info(f"测试情况: {case['desc']}")
            best_params = case["best_params"]
            
            # 模拟原文第7214-7264行的逻辑
            try:
                # 获取学习率
                if best_params and 'learning_rate' in best_params:
                    learning_rate = best_params['learning_rate']
                    logging.info(f"✅ 使用优化的学习率: {learning_rate}")
                else:
                    learning_rate = MockConfig.DEFAULT_LEARNING_RATE
                    logging.info(f"⚠️ 使用默认学习率: {learning_rate}")
                
                # 构建优化器
                if MockConfig.USE_ADEMAMIX:
                    # 这里模拟AdEMAMix优化器
                    optimizer = "AdEMAMix_optimizer"
                    logging.info("使用AdEMAMix优化器")
                else:
                    optimizer = tf.keras.optimizers.Adam(
                        learning_rate=float(learning_rate),
                        clipnorm=MockConfig.GRADIENT_CLIP_NORM,
                        beta_1=0.9,
                        beta_2=0.999,
                        epsilon=1e-7
                    )
                    logging.info("使用Adam优化器")
                    
            except (ValueError, TypeError) as e:
                logging.error(f"参数转换失败: {str(e)}，使用默认值")
                learning_rate = MockConfig.DEFAULT_LEARNING_RATE
                optimizer = tf.keras.optimizers.Adam(
                    learning_rate=float(learning_rate),
                    clipnorm=MockConfig.GRADIENT_CLIP_NORM,
                    beta_1=0.9,
                    beta_2=0.999,
                    epsilon=1e-7
                )
                logging.info("使用默认Adam优化器")
            
            # 🔧 修复：确保optimizer在所有代码路径中都有定义
            if 'optimizer' not in locals():
                logging.warning("⚠️ optimizer未定义，使用默认Adam优化器")
                learning_rate = MockConfig.DEFAULT_LEARNING_RATE
                optimizer = tf.keras.optimizers.Adam(
                    learning_rate=float(learning_rate),
                    clipnorm=MockConfig.GRADIENT_CLIP_NORM,
                    beta_1=0.9,
                    beta_2=0.999,
                    epsilon=1e-7
                )
                logging.info("使用默认Adam优化器")
            
            # 验证optimizer已定义
            if optimizer is not None:
                logging.info(f"✅ {case['desc']}: optimizer定义成功")
            else:
                logging.error(f"❌ {case['desc']}: optimizer未定义")
                return False
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 场景2测试失败: {str(e)}")
        return False

def test_scenario_3_metric_matching():
    """场景3: 指标匹配 - 测试动态指标匹配修复"""
    logging.info("🔧 场景3: 测试指标匹配场景...")
    
    try:
        # 模拟不同训练轮次的指标名称变化
        test_scenarios = [
            {
                "desc": "第1轮训练",
                "metrics": ['val_loss', 'val_classification_output_1_auc', 'val_classification_output_1_accuracy']
            },
            {
                "desc": "第2轮训练（带后缀）",
                "metrics": ['val_loss', 'val_classification_output_1_auc_1', 'val_classification_output_1_accuracy_1']
            },
            {
                "desc": "第3轮训练（复杂后缀）",
                "metrics": ['val_loss', 'val_classification_output_1_auc_2', 'val_binary_accuracy']
            },
            {
                "desc": "异常情况（无AUC指标）",
                "metrics": ['val_loss', 'val_accuracy', 'val_precision']
            }
        ]
        
        def _find_dynamic_auc_metric(available_metrics):
            """🔧 修复后的动态查找AUC指标函数"""
            import re
            
            logging.info(f"🔍 开始动态匹配指标，可用指标: {available_metrics}")
            
            # 定义AUC指标的正则表达式模式（修复后的更宽松匹配）
            auc_patterns = [
                r'val_classification_output_1_auc.*',  # 更宽松的匹配
                r'val_.*classification_output_1.*auc.*',
                r'val_auc.*',
                r'val_binary_accuracy.*'
            ]
            
            # 搜索匹配的AUC指标
            for pattern in auc_patterns:
                matching_keys = [k for k in available_metrics if re.search(pattern, k)]
                if matching_keys:
                    # 选择最新的指标（按名称排序，取最后一个）
                    latest_key = sorted(matching_keys)[-1]
                    logging.info(f"✅ 动态匹配找到AUC指标: {latest_key} (模式: {pattern})")
                    return latest_key
            
            # 如果都没找到，使用accuracy作为替代
            acc_patterns = [
                r'val_classification_output_1.*accuracy.*',
                r'val_.*accuracy.*',
                r'val_binary_accuracy.*'
            ]
            for pattern in acc_patterns:
                matching_keys = [k for k in available_metrics if re.search(pattern, k)]
                if matching_keys:
                    latest_key = sorted(matching_keys)[-1]
                    logging.info(f"✅ 动态匹配找到accuracy指标作为替代: {latest_key} (模式: {pattern})")
                    return latest_key
            
            logging.warning(f"⚠️ 无法找到任何AUC或accuracy指标，可用指标: {available_metrics}")
            return None
        
        # 测试各种场景
        for scenario in test_scenarios:
            logging.info(f"测试场景: {scenario['desc']}")
            result = _find_dynamic_auc_metric(scenario['metrics'])
            
            if result:
                logging.info(f"✅ {scenario['desc']}: 成功匹配指标 {result}")
            else:
                logging.warning(f"⚠️ {scenario['desc']}: 未找到匹配指标，将使用val_loss")
        
        return True

    except Exception as e:
        logging.error(f"❌ 场景3测试失败: {str(e)}")
        return False

def test_scenario_4_prediction_processing():
    """场景4: 预测处理 - 测试market_types的.values错误修复"""
    logging.info("🔧 场景4: 测试预测处理场景...")

    try:
        # 加载P.pull模块
        P_pull = load_module_from_file('/home/<USER>/P.pull.py', 'P_pull')

        # 模拟预测场景的数据
        ts_codes = ['000001.SZ', '600000.SH', '300001.SZ', '688001.SH', '430001.BJ'] * 10
        strategy_type = '首板'

        # 模拟预测结果
        predictions = [
            np.random.rand(50, 1),  # 次日涨停概率
            np.random.rand(50, 1),  # 次日涨幅
            np.random.rand(50, 1),  # 后日涨停概率
            np.random.rand(50, 1)   # 后日涨幅
        ]

        logging.info(f"模拟预测数据: {len(ts_codes)}只股票，4个输出")

        # 🔧 测试修复后的代码逻辑（原文第10015-10018行）
        ts_codes_series = pd.Series(ts_codes)
        # 修复前: market_types = vectorized_get_market_type(ts_codes_series).values  # 会报错
        # 修复后:
        market_types = P_pull.vectorized_get_market_type(ts_codes_series)

        logging.info(f"✅ 市场类型获取成功，类型: {type(market_types)}")
        logging.info(f"✅ 市场类型样本: {market_types[:5]}")

        # 测试后续的向量化操作
        main_mask = market_types == 'MAIN'
        chinext_mask = market_types == 'CHINEXT'
        star_mask = market_types == 'STAR'
        bse_mask = market_types == 'BSE'

        logging.info(f"✅ 向量化掩码操作成功")
        logging.info(f"  MAIN股票数: {np.sum(main_mask)}")
        logging.info(f"  CHINEXT股票数: {np.sum(chinext_mask)}")
        logging.info(f"  STAR股票数: {np.sum(star_mask)}")
        logging.info(f"  BSE股票数: {np.sum(bse_mask)}")

        # 模拟反标准化参数设置
        DEFAULT_NORMALIZATION_PARAMS = {
            '首板': {
                'MAIN': {
                    'regression_output_1': {'median': 1.0, 'iqr': 8.0},
                    'regression_output_2': {'median': 1.0, 'iqr': 8.0}
                },
                'CHINEXT': {
                    'regression_output_1': {'median': 1.2, 'iqr': 10.0},
                    'regression_output_2': {'median': 1.2, 'iqr': 10.0}
                }
            }
        }

        # 向量化参数获取
        next_medians = np.zeros(len(ts_codes))
        next_iqrs = np.zeros(len(ts_codes))
        second_medians = np.zeros(len(ts_codes))
        second_iqrs = np.zeros(len(ts_codes))

        if strategy_type in DEFAULT_NORMALIZATION_PARAMS:
            param_map = DEFAULT_NORMALIZATION_PARAMS[strategy_type]

            # 批量设置MAIN参数
            if np.any(main_mask) and 'MAIN' in param_map:
                params = param_map['MAIN']
                next_medians[main_mask] = params['regression_output_1']['median']
                next_iqrs[main_mask] = params['regression_output_1']['iqr']
                second_medians[main_mask] = params['regression_output_2']['median']
                second_iqrs[main_mask] = params['regression_output_2']['iqr']

            # 批量设置CHINEXT参数
            if np.any(chinext_mask) and 'CHINEXT' in param_map:
                params = param_map['CHINEXT']
                next_medians[chinext_mask] = params['regression_output_1']['median']
                next_iqrs[chinext_mask] = params['regression_output_1']['iqr']
                second_medians[chinext_mask] = params['regression_output_2']['median']
                second_iqrs[chinext_mask] = params['regression_output_2']['iqr']

        # 设置默认值
        default_mask = (next_medians == 0) & (next_iqrs == 0)
        if np.any(default_mask):
            next_medians[default_mask] = 1.0
            next_iqrs[default_mask] = 8.0
            second_medians[default_mask] = 1.0
            second_iqrs[default_mask] = 8.0

        # 向量化反标准化
        raw_next_returns = predictions[1].flatten()
        raw_second_returns = predictions[3].flatten()

        processed_next_returns = (raw_next_returns * next_iqrs) + next_medians
        processed_second_returns = (raw_second_returns * second_iqrs) + second_medians

        logging.info(f"✅ 反标准化处理成功")
        logging.info(f"  处理后次日涨幅范围: {processed_next_returns.min():.4f} - {processed_next_returns.max():.4f}")
        logging.info(f"  处理后后日涨幅范围: {processed_second_returns.min():.4f} - {processed_second_returns.max():.4f}")

        # 创建结果DataFrame
        results_df = pd.DataFrame({
            'ts_code': ts_codes,
            'probability': predictions[0].flatten(),
            'predicted_next_day_return': processed_next_returns,
            'third_day_prob': predictions[2].flatten(),
            'predicted_second_day_return': processed_second_returns
        })

        logging.info(f"✅ 预测结果DataFrame创建成功，形状: {results_df.shape}")
        logging.info(f"✅ 样本结果:\n{results_df.head()}")

        return True

    except Exception as e:
        logging.error(f"❌ 场景4测试失败: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return False

def test_scenario_5_integrated_workflow():
    """场景5: 集成工作流 - 测试完整的训练预测流程"""
    logging.info("🔧 场景5: 测试集成工作流场景...")

    try:
        # 模拟完整的工作流程
        logging.info("开始模拟完整工作流...")

        # 1. 数据准备阶段
        stock_data, X_train, y_train, X_val, y_val = create_mock_data()
        logging.info("✅ 数据准备完成")

        # 2. 超参数优化阶段（模拟多个trial）
        class MockTrial:
            def __init__(self, number):
                self.number = number

        trials = [MockTrial(i) for i in range(1, 4)]
        best_trial = None
        best_score = 0

        for trial in trials:
            # 使用修复后的time变量逻辑
            import time as time_module
            trial_unique_id = f"{trial.number}_{int(time_module.time() * 1000) % 10000}"

            # 模拟训练过程
            mock_score = np.random.rand()
            logging.info(f"Trial {trial.number} (ID: {trial_unique_id}): 模拟得分 {mock_score:.4f}")

            if mock_score > best_score:
                best_score = mock_score
                best_trial = trial

        logging.info(f"✅ 超参数优化完成，最佳Trial: {best_trial.number}")

        # 3. 模型训练阶段（使用修复后的optimizer逻辑）
        best_params = {"learning_rate": 0.01} if best_trial else None

        class MockConfig:
            USE_ADEMAMIX = False
            DEFAULT_LEARNING_RATE = 0.001
            GRADIENT_CLIP_NORM = 1.0

        # 获取学习率
        if best_params and 'learning_rate' in best_params:
            learning_rate = best_params['learning_rate']
        else:
            learning_rate = MockConfig.DEFAULT_LEARNING_RATE

        # 创建优化器
        optimizer = tf.keras.optimizers.Adam(
            learning_rate=float(learning_rate),
            clipnorm=MockConfig.GRADIENT_CLIP_NORM
        )

        logging.info(f"✅ 模型训练配置完成，学习率: {learning_rate}")

        # 4. 指标监控阶段（使用修复后的动态指标匹配）
        training_metrics = ['val_loss', 'val_classification_output_1_auc_1', 'val_accuracy']

        import re
        auc_patterns = [r'val_classification_output_1_auc.*', r'val_auc.*']
        matched_metric = None

        for pattern in auc_patterns:
            matching_keys = [k for k in training_metrics if re.search(pattern, k)]
            if matching_keys:
                matched_metric = sorted(matching_keys)[-1]
                break

        logging.info(f"✅ 指标匹配完成，使用指标: {matched_metric}")

        # 5. 预测阶段（使用修复后的market_types逻辑）
        # 这里简化测试，不加载实际模块
        ts_codes = ['000001.SZ', '600000.SH', '300001.SZ']

        # 模拟市场类型获取（不调用.values）
        mock_market_types = np.array(['MAIN', 'MAIN', 'CHINEXT'])

        # 模拟预测结果处理
        mock_predictions = [np.random.rand(3, 1) for _ in range(4)]

        results_df = pd.DataFrame({
            'ts_code': ts_codes,
            'probability': mock_predictions[0].flatten(),
            'market_type': mock_market_types
        })

        logging.info(f"✅ 预测处理完成，结果形状: {results_df.shape}")

        logging.info("✅ 集成工作流测试完成，所有修复点都正常工作")
        return True

    except Exception as e:
        logging.error(f"❌ 场景5测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logging.info("=" * 80)
    logging.info("🚀 基于原文结构场景的修复验证测试开始")
    logging.info(f"测试时间: {datetime.now()}")
    logging.info("=" * 80)

    scenarios = [
        ("场景1: 数据集创建", test_scenario_1_dataset_creation),
        ("场景2: 模型训练", test_scenario_2_model_training),
        ("场景3: 指标匹配", test_scenario_3_metric_matching),
        ("场景4: 预测处理", test_scenario_4_prediction_processing),
        ("场景5: 集成工作流", test_scenario_5_integrated_workflow)
    ]

    results = []

    for scenario_name, test_func in scenarios:
        logging.info(f"\n{'='*60}")
        logging.info(f"🔧 运行测试: {scenario_name}")
        logging.info(f"{'='*60}")

        try:
            result = test_func()
            results.append((scenario_name, result))

            if result:
                logging.info(f"✅ {scenario_name} - 通过")
            else:
                logging.error(f"❌ {scenario_name} - 失败")

        except Exception as e:
            logging.error(f"❌ {scenario_name} - 异常: {str(e)}")
            results.append((scenario_name, False))

    # 输出测试总结
    logging.info(f"\n{'='*80}")
    logging.info("📊 基于场景的修复验证测试总结")
    logging.info(f"{'='*80}")

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for scenario_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"{scenario_name}: {status}")

    logging.info(f"\n📈 测试统计:")
    logging.info(f"  总场景数: {total}")
    logging.info(f"  通过数: {passed}")
    logging.info(f"  失败数: {total - passed}")
    logging.info(f"  成功率: {passed/total*100:.1f}%")

    if passed == total:
        logging.info(f"\n🎉 所有场景测试通过！所有修复都正确工作！")
        return True
    else:
        logging.error(f"\n⚠️ 有 {total - passed} 个场景失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
