#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量化批次处理性能测试
对比原始逐股票处理和向量化处理的性能差异
"""

import pandas as pd
import numpy as np
import time
import logging
from typing import List
import sys
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def generate_test_data(n_stocks: int = 100, n_days: int = 100) -> pd.DataFrame:
    """生成测试数据"""
    logging.info(f"生成测试数据: {n_stocks}只股票，{n_days}天数据")
    
    test_data = []
    np.random.seed(42)
    
    for i in range(n_stocks):
        stock_code = f"{i:06d}.SZ"
        base_price = 10 + np.random.randn() * 2
        
        for j in range(n_days):
            # 模拟价格随机游走
            price_change = np.random.randn() * 0.02
            base_price = max(1.0, base_price * (1 + price_change))
            
            high = base_price * (1 + abs(np.random.randn() * 0.01))
            low = base_price * (1 - abs(np.random.randn() * 0.01))
            open_price = base_price + np.random.randn() * 0.01
            close = base_price + np.random.randn() * 0.01
            
            test_data.append({
                'ts_code': stock_code,
                'trade_date': f"2024{(j//30+1):02d}{(j%30+1):02d}",
                'open': max(0.1, open_price),
                'high': max(0.1, high),
                'low': max(0.1, low),
                'close': max(0.1, close),
                'vol': np.random.randint(1000, 100000),
                'turnover_rate': np.random.uniform(0.1, 10.0),
                'pre_close': max(0.1, base_price * 0.99)
            })
    
    df = pd.DataFrame(test_data)
    logging.info(f"测试数据生成完成: {len(df)} 行")
    return df


def simulate_original_processing(stock_codes: List[str], dataframe: pd.DataFrame) -> float:
    """模拟原始的逐股票处理方式"""
    logging.info(f"开始模拟原始处理: {len(stock_codes)}只股票")
    start_time = time.time()
    
    results = []
    for i, ts_code in enumerate(stock_codes):
        # 模拟单股票处理的开销
        sub_df = dataframe[dataframe['ts_code'] == ts_code].copy()
        
        if not sub_df.empty:
            # 模拟技术指标计算（简化版本）
            sub_df['ma5'] = sub_df['close'].rolling(window=5, min_periods=1).mean()
            sub_df['ma10'] = sub_df['close'].rolling(window=10, min_periods=1).mean()
            sub_df['ma20'] = sub_df['close'].rolling(window=20, min_periods=1).mean()
            
            # 模拟更多计算
            sub_df['volume_ma5'] = sub_df['vol'].rolling(window=5, min_periods=1).mean()
            sub_df['price_trend'] = (sub_df['ma5'] > sub_df['ma10']).astype(int)
            
            results.append(sub_df)
        
        # 模拟进度日志
        if i % max(1, len(stock_codes) // 4) == 0:
            progress = (i + 1) / len(stock_codes) * 100
            logging.info(f"原始处理进度: {i+1}/{len(stock_codes)} ({progress:.1f}%) - {ts_code}")
    
    # 合并结果
    if results:
        result_df = pd.concat(results, ignore_index=True)
    else:
        result_df = pd.DataFrame()
    
    elapsed_time = time.time() - start_time
    logging.info(f"原始处理完成，耗时: {elapsed_time:.2f}秒")
    return elapsed_time


def simulate_vectorized_processing(stock_codes: List[str], dataframe: pd.DataFrame) -> float:
    """模拟向量化处理方式"""
    logging.info(f"开始向量化处理: {len(stock_codes)}只股票")
    start_time = time.time()
    
    # 筛选数据
    batch_df = dataframe[dataframe['ts_code'].isin(stock_codes)].copy()
    batch_df = batch_df.sort_values(['ts_code', 'trade_date']).reset_index(drop=True)
    
    # 向量化计算技术指标
    batch_df['ma5'] = batch_df.groupby('ts_code')['close'].transform(
        lambda x: x.rolling(window=5, min_periods=1).mean()
    )
    batch_df['ma10'] = batch_df.groupby('ts_code')['close'].transform(
        lambda x: x.rolling(window=10, min_periods=1).mean()
    )
    batch_df['ma20'] = batch_df.groupby('ts_code')['close'].transform(
        lambda x: x.rolling(window=20, min_periods=1).mean()
    )
    
    # 向量化计算成交量指标
    batch_df['volume_ma5'] = batch_df.groupby('ts_code')['vol'].transform(
        lambda x: x.rolling(window=5, min_periods=1).mean()
    )
    
    # 向量化计算趋势指标
    batch_df['price_trend'] = (batch_df['ma5'] > batch_df['ma10']).astype(int)
    
    # 向量化计算布林带（简化版）
    def calculate_bollinger_simple(group):
        close = group['close']
        middle = close.rolling(window=20, min_periods=1).mean()
        std = close.rolling(window=20, min_periods=1).std()
        upper = middle + (std * 2)
        lower = middle - (std * 2)
        
        return pd.DataFrame({
            'boll_upper': upper,
            'boll_middle': middle,
            'boll_lower': lower
        }, index=group.index)
    
    boll_results = batch_df.groupby('ts_code').apply(calculate_bollinger_simple)
    boll_results.index = boll_results.index.droplevel(0)
    batch_df = batch_df.join(boll_results)
    
    # 向量化计算换手率特征
    if 'turnover_rate' in batch_df.columns:
        batch_df['turnover_rate_ma5'] = batch_df.groupby('ts_code')['turnover_rate'].transform(
            lambda x: x.rolling(window=5, min_periods=1).mean()
        )
        batch_df['turnover_rate_change'] = batch_df.groupby('ts_code')['turnover_rate'].transform(
            lambda x: x.pct_change() * 100
        )
    
    elapsed_time = time.time() - start_time
    logging.info(f"向量化处理完成，耗时: {elapsed_time:.2f}秒")
    return elapsed_time


def run_performance_comparison():
    """运行性能对比测试"""
    print("=" * 60)
    print("向量化批次处理性能对比测试")
    print("=" * 60)
    
    # 测试不同规模的数据
    test_cases = [
        (50, 100),   # 50只股票，100天
        (100, 100),  # 100只股票，100天
        (200, 100),  # 200只股票，100天
        (338, 100),  # 338只股票，100天（接近实际场景）
    ]
    
    results = []
    
    for n_stocks, n_days in test_cases:
        print(f"\n📊 测试场景: {n_stocks}只股票，{n_days}天数据")
        print("-" * 40)
        
        # 生成测试数据
        df = generate_test_data(n_stocks, n_days)
        stock_codes = df['ts_code'].unique().tolist()
        
        # 测试原始处理方式
        original_time = simulate_original_processing(stock_codes, df)
        
        # 测试向量化处理方式
        vectorized_time = simulate_vectorized_processing(stock_codes, df)
        
        # 计算性能提升
        speedup = original_time / vectorized_time if vectorized_time > 0 else 0
        
        result = {
            'stocks': n_stocks,
            'days': n_days,
            'data_rows': len(df),
            'original_time': original_time,
            'vectorized_time': vectorized_time,
            'speedup': speedup
        }
        results.append(result)
        
        print(f"原始处理时间: {original_time:.2f}秒")
        print(f"向量化处理时间: {vectorized_time:.2f}秒")
        print(f"性能提升: {speedup:.1f}x")
        print(f"数据量: {len(df):,} 行")
    
    # 输出总结
    print("\n" + "=" * 60)
    print("性能对比总结")
    print("=" * 60)
    
    for result in results:
        print(f"📈 {result['stocks']}只股票 ({result['data_rows']:,}行): "
              f"原始 {result['original_time']:.2f}s → 向量化 {result['vectorized_time']:.2f}s "
              f"(提升 {result['speedup']:.1f}x)")
    
    # 计算平均性能提升
    avg_speedup = np.mean([r['speedup'] for r in results])
    print(f"\n🚀 平均性能提升: {avg_speedup:.1f}x")
    
    # 估算实际场景的性能改进
    print(f"\n💡 实际场景估算:")
    print(f"   - 如果原来处理338只股票需要10分钟")
    print(f"   - 向量化处理预计只需要 {10/avg_speedup:.1f}分钟")
    print(f"   - 节省时间: {10-10/avg_speedup:.1f}分钟 ({(1-1/avg_speedup)*100:.1f}%)")


if __name__ == "__main__":
    run_performance_comparison()
