#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AdEMAMix优化器场景测试
基于原文结构的场景测试，验证AdEMAMix优化器的正确性和性能

测试场景：
1. 优化器初始化和参数验证
2. 梯度更新机制验证
3. 长期EMA维护测试
4. 与Adam优化器性能对比
5. 金融数据训练场景测试
"""

import sys
import os
import numpy as np
import tensorflow as tf
import logging
import time
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入AdEMAMix优化器
try:
    # 直接从P.pull.py导入，避免P.py的XGBoost依赖问题
    import importlib.util
    spec = importlib.util.spec_from_file_location("P_pull", "P.pull.py")
    P_pull = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(P_pull)

    AdEMAMix = P_pull.AdEMAMix
    Config = P_pull.Config
    logging.info("✅ 成功从P.pull.py导入AdEMAMix优化器")
except ImportError as e:
    logging.error(f"❌ 无法导入AdEMAMix优化器: {e}")
    sys.exit(1)


class AdEMAMixScenarioTester:
    """AdEMAMix优化器场景测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        
    def scenario_1_optimizer_initialization(self) -> bool:
        """场景1: 优化器初始化和参数验证"""
        logging.info("🧪 场景1: AdEMAMix优化器初始化测试")
        
        try:
            # 测试默认参数初始化
            optimizer_default = AdEMAMix()
            assert optimizer_default.learning_rate == 1e-4
            assert optimizer_default.beta_1 == 0.9
            assert optimizer_default.beta_2 == 0.999
            assert optimizer_default.alpha == 0.5
            assert optimizer_default.epsilon == 1e-8
            assert optimizer_default.weight_decay == 0.0
            logging.info("✅ 默认参数初始化正确")
            
            # 测试自定义参数初始化
            optimizer_custom = AdEMAMix(
                learning_rate=1e-3,
                beta_1=0.95,
                beta_2=0.99,
                alpha=0.3,
                epsilon=1e-7,
                weight_decay=0.01
            )
            assert optimizer_custom.learning_rate == 1e-3
            assert optimizer_custom.alpha == 0.3
            assert optimizer_custom.weight_decay == 0.01
            logging.info("✅ 自定义参数初始化正确")
            
            # 测试长期EMA参数
            assert optimizer_custom.beta_long == 0.999
            assert isinstance(optimizer_custom._long_term_ema, dict)
            logging.info("✅ 长期EMA参数初始化正确")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 场景1失败: {e}")
            return False
    
    def scenario_2_gradient_update_mechanism(self) -> bool:
        """场景2: 梯度更新机制验证"""
        logging.info("🧪 场景2: AdEMAMix梯度更新机制测试")
        
        try:
            # 创建简单的测试模型
            model = tf.keras.Sequential([
                tf.keras.layers.Dense(10, input_shape=(5,)),
                tf.keras.layers.Dense(1)
            ])
            
            # 创建AdEMAMix优化器
            optimizer = AdEMAMix(learning_rate=1e-3)
            
            # 生成测试数据
            x_test = tf.random.normal((32, 5))
            y_test = tf.random.normal((32, 1))
            
            # 记录初始权重
            initial_weights = [w.numpy().copy() for w in model.trainable_variables]
            
            # 执行一次前向传播和反向传播
            with tf.GradientTape() as tape:
                predictions = model(x_test, training=True)
                loss = tf.keras.losses.mse(y_test, predictions)
            
            gradients = tape.gradient(loss, model.trainable_variables)
            
            # 应用梯度更新
            optimizer.apply_gradients(zip(gradients, model.trainable_variables))
            
            # 验证权重已更新
            updated_weights = [w.numpy() for w in model.trainable_variables]
            weights_changed = any(
                not np.allclose(initial, updated, atol=1e-8)
                for initial, updated in zip(initial_weights, updated_weights)
            )
            
            assert weights_changed, "权重应该在梯度更新后发生变化"
            logging.info("✅ 梯度更新机制正常工作")
            
            # 验证长期EMA已创建
            assert len(optimizer._long_term_ema) > 0, "长期EMA应该已创建"
            logging.info(f"✅ 长期EMA已创建，包含 {len(optimizer._long_term_ema)} 个变量")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 场景2失败: {e}")
            return False
    
    def scenario_3_long_term_ema_maintenance(self) -> bool:
        """场景3: 长期EMA维护测试"""
        logging.info("🧪 场景3: 长期EMA维护测试")
        
        try:
            # 创建测试模型
            model = tf.keras.Sequential([
                tf.keras.layers.Dense(5, input_shape=(3,)),
                tf.keras.layers.Dense(1)
            ])
            
            optimizer = AdEMAMix(learning_rate=1e-3, beta_long=0.99)
            
            # 生成测试数据
            x_test = tf.random.normal((16, 3))
            y_test = tf.random.normal((16, 1))
            
            # 执行多次更新以测试EMA维护
            ema_values = []
            for step in range(5):
                with tf.GradientTape() as tape:
                    predictions = model(x_test, training=True)
                    loss = tf.keras.losses.mse(y_test, predictions)
                
                gradients = tape.gradient(loss, model.trainable_variables)
                optimizer.apply_gradients(zip(gradients, model.trainable_variables))
                
                # 记录EMA值
                if optimizer._long_term_ema:
                    first_var_name = list(optimizer._long_term_ema.keys())[0]
                    ema_value = optimizer._long_term_ema[first_var_name].numpy().copy()
                    ema_values.append(ema_value)
            
            # 验证EMA值在变化
            assert len(ema_values) >= 2, "应该有多个EMA值记录"
            
            # 验证EMA值确实在更新
            ema_changed = not np.allclose(ema_values[0], ema_values[-1], atol=1e-8)
            assert ema_changed, "长期EMA值应该在多次更新后发生变化"
            
            logging.info("✅ 长期EMA维护机制正常工作")
            return True
            
        except Exception as e:
            logging.error(f"❌ 场景3失败: {e}")
            return False
    
    def scenario_4_performance_comparison(self) -> bool:
        """场景4: 与Adam优化器性能对比"""
        logging.info("🧪 场景4: AdEMAMix vs Adam性能对比测试")
        
        try:
            # 创建相同的测试问题
            def create_test_model():
                return tf.keras.Sequential([
                    tf.keras.layers.Dense(20, activation='relu', input_shape=(10,)),
                    tf.keras.layers.Dense(10, activation='relu'),
                    tf.keras.layers.Dense(1)
                ])
            
            # 生成测试数据
            np.random.seed(42)
            tf.random.set_seed(42)
            x_train = tf.random.normal((1000, 10))
            y_train = tf.reduce_sum(x_train[:, :5], axis=1, keepdims=True) + tf.random.normal((1000, 1)) * 0.1
            
            # 测试AdEMAMix
            model_ademamix = create_test_model()
            optimizer_ademamix = AdEMAMix(learning_rate=1e-3)
            
            losses_ademamix = []
            for epoch in range(10):
                with tf.GradientTape() as tape:
                    predictions = model_ademamix(x_train, training=True)
                    loss = tf.keras.losses.mse(y_train, predictions)
                
                gradients = tape.gradient(loss, model_ademamix.trainable_variables)
                optimizer_ademamix.apply_gradients(zip(gradients, model_ademamix.trainable_variables))
                losses_ademamix.append(float(loss))
            
            # 测试Adam
            tf.random.set_seed(42)  # 重置随机种子确保公平比较
            model_adam = create_test_model()
            optimizer_adam = tf.keras.optimizers.Adam(learning_rate=1e-3)
            
            losses_adam = []
            for epoch in range(10):
                with tf.GradientTape() as tape:
                    predictions = model_adam(x_train, training=True)
                    loss = tf.keras.losses.mse(y_train, predictions)
                
                gradients = tape.gradient(loss, model_adam.trainable_variables)
                optimizer_adam.apply_gradients(zip(gradients, model_adam.trainable_variables))
                losses_adam.append(float(loss))
            
            # 比较最终损失
            final_loss_ademamix = losses_ademamix[-1]
            final_loss_adam = losses_adam[-1]
            
            logging.info(f"AdEMAMix最终损失: {final_loss_ademamix:.6f}")
            logging.info(f"Adam最终损失: {final_loss_adam:.6f}")
            
            # 存储性能指标
            self.performance_metrics['ademamix_losses'] = losses_ademamix
            self.performance_metrics['adam_losses'] = losses_adam
            
            # 验证AdEMAMix至少不比Adam差太多
            performance_ratio = final_loss_ademamix / final_loss_adam
            assert performance_ratio < 2.0, f"AdEMAMix性能不应该比Adam差太多，比率: {performance_ratio}"
            
            logging.info("✅ AdEMAMix与Adam性能对比测试通过")
            return True
            
        except Exception as e:
            logging.error(f"❌ 场景4失败: {e}")
            return False
    
    def scenario_5_financial_data_training(self) -> bool:
        """场景5: 金融数据训练场景测试"""
        logging.info("🧪 场景5: 金融数据训练场景测试")
        
        try:
            # 模拟金融时序数据
            np.random.seed(42)
            sequence_length = 20
            feature_count = 15
            batch_size = 64
            
            # 生成模拟的金融特征数据
            x_financial = np.random.randn(batch_size, sequence_length, feature_count)
            
            # 添加一些金融数据的特征（趋势、波动性等）
            for i in range(batch_size):
                trend = np.linspace(0, np.random.randn() * 0.1, sequence_length)
                x_financial[i, :, 0] += trend  # 价格趋势
                x_financial[i, :, 1] = np.abs(x_financial[i, :, 1])  # 成交量（正值）
                x_financial[i, :, 2] = np.cumsum(x_financial[i, :, 2] * 0.01)  # 累积收益
            
            # 生成目标：分类（涨跌）和回归（涨跌幅）
            y_classification = (x_financial[:, -1, 0] > x_financial[:, 0, 0]).astype(np.float32)
            y_regression = (x_financial[:, -1, 0] - x_financial[:, 0, 0]) / x_financial[:, 0, 0]
            
            # 创建金融预测模型（类似P.py中的结构）
            inputs = tf.keras.Input(shape=(sequence_length, feature_count))
            
            # LSTM层处理时序特征
            lstm_out = tf.keras.layers.LSTM(32, return_sequences=False)(inputs)
            lstm_out = tf.keras.layers.Dropout(0.2)(lstm_out)
            
            # 分类输出
            classification_output = tf.keras.layers.Dense(1, activation='sigmoid', name='classification')(lstm_out)
            
            # 回归输出
            regression_output = tf.keras.layers.Dense(1, name='regression')(lstm_out)
            
            model = tf.keras.Model(inputs=inputs, outputs=[classification_output, regression_output])
            
            # 使用AdEMAMix优化器
            optimizer = AdEMAMix(
                learning_rate=1e-3,
                beta_1=0.9,
                beta_2=0.999,
                alpha=0.5,
                weight_decay=0.01
            )
            
            # 训练几个步骤
            training_losses = []
            for step in range(5):
                with tf.GradientTape() as tape:
                    predictions = model(x_financial, training=True)
                    
                    # 计算损失
                    classification_loss = tf.keras.losses.binary_crossentropy(
                        y_classification, predictions[0][:, 0]
                    )
                    regression_loss = tf.keras.losses.mse(
                        y_regression, predictions[1][:, 0]
                    )
                    
                    total_loss = tf.reduce_mean(classification_loss) + tf.reduce_mean(regression_loss)
                
                gradients = tape.gradient(total_loss, model.trainable_variables)
                optimizer.apply_gradients(zip(gradients, model.trainable_variables))
                
                training_losses.append(float(total_loss))
                logging.info(f"步骤 {step + 1}: 损失 = {float(total_loss):.6f}")
            
            # 验证训练过程
            assert len(training_losses) == 5, "应该有5个训练损失记录"
            assert all(isinstance(loss, float) and not np.isnan(loss) for loss in training_losses), "所有损失值应该是有效的数字"
            
            # 验证损失趋势（通常应该下降或至少稳定）
            if len(training_losses) >= 3:
                recent_trend = np.mean(training_losses[-2:]) <= np.mean(training_losses[:2]) * 1.1
                if recent_trend:
                    logging.info("✅ 训练损失趋势良好")
                else:
                    logging.warning("⚠️ 训练损失趋势不理想，但测试继续")
            
            logging.info("✅ 金融数据训练场景测试通过")
            return True
            
        except Exception as e:
            logging.error(f"❌ 场景5失败: {e}")
            return False
    
    def run_all_scenarios(self) -> Dict[str, bool]:
        """运行所有场景测试"""
        logging.info("🚀 开始AdEMAMix优化器场景测试")
        
        scenarios = [
            ("优化器初始化", self.scenario_1_optimizer_initialization),
            ("梯度更新机制", self.scenario_2_gradient_update_mechanism),
            ("长期EMA维护", self.scenario_3_long_term_ema_maintenance),
            ("性能对比", self.scenario_4_performance_comparison),
            ("金融数据训练", self.scenario_5_financial_data_training),
        ]
        
        results = {}
        passed_count = 0
        
        for name, test_func in scenarios:
            try:
                result = test_func()
                results[name] = result
                if result:
                    passed_count += 1
                    logging.info(f"✅ {name} - 通过")
                else:
                    logging.error(f"❌ {name} - 失败")
            except Exception as e:
                results[name] = False
                logging.error(f"❌ {name} - 异常: {e}")
        
        # 总结
        total_tests = len(scenarios)
        logging.info(f"\n📊 测试总结: {passed_count}/{total_tests} 个场景通过")
        
        if passed_count == total_tests:
            logging.info("🎉 所有AdEMAMix场景测试通过！")
        else:
            logging.warning(f"⚠️ {total_tests - passed_count} 个场景测试失败")
        
        self.test_results = results
        return results


def main():
    """主函数"""
    print("=" * 60)
    print("AdEMAMix优化器场景测试")
    print("基于原文结构的场景验证")
    print("=" * 60)
    
    # 创建测试器
    tester = AdEMAMixScenarioTester()
    
    # 运行所有测试
    results = tester.run_all_scenarios()
    
    # 输出详细结果
    print("\n" + "=" * 60)
    print("详细测试结果:")
    for scenario, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {scenario}: {status}")
    
    # 性能指标可视化（如果有的话）
    if hasattr(tester, 'performance_metrics') and tester.performance_metrics:
        try:
            plt.figure(figsize=(10, 6))
            if 'ademamix_losses' in tester.performance_metrics:
                plt.plot(tester.performance_metrics['ademamix_losses'], 'b-', label='AdEMAMix', linewidth=2)
            if 'adam_losses' in tester.performance_metrics:
                plt.plot(tester.performance_metrics['adam_losses'], 'r--', label='Adam', linewidth=2)
            plt.xlabel('训练轮次')
            plt.ylabel('损失值')
            plt.title('AdEMAMix vs Adam 性能对比')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.savefig('ademamix_performance_comparison.png', dpi=300, bbox_inches='tight')
            print(f"\n📈 性能对比图已保存为: ademamix_performance_comparison.png")
        except Exception as e:
            print(f"⚠️ 无法生成性能对比图: {e}")
    
    print("=" * 60)
    return all(results.values())


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
