#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终综合清理测试
验证P.pull.py中的所有修复和清理工作：
1. 日志错误修复验证
2. 代码清理验证
3. 向量化优化验证
4. 整体质量验证
"""

import sys
import os
import re
import logging
import time
import pandas as pd
import numpy as np
from datetime import datetime

# 设置日志
log_filename = f"final_comprehensive_cleanup_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)

def read_file_content(file_path):
    """读取文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logging.error(f"读取文件失败: {e}")
        return None

def test_error_fixes():
    """测试日志错误修复"""
    logging.info("🔧 测试日志错误修复...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    results = []
    
    # 1. 检查optimizer作用域修复
    optimizer_in_try = len(re.findall(r'# 🔧 修复：在try块中也定义optimizer', content))
    if optimizer_in_try >= 1:
        logging.info("✅ optimizer作用域错误已修复")
        results.append(True)
    else:
        logging.error("❌ optimizer作用域错误未修复")
        results.append(False)
    
    # 2. 检查AUC指标动态匹配修复
    dynamic_auc = len(re.findall(r'_find_dynamic_auc_metric|动态匹配找到AUC指标', content))
    if dynamic_auc >= 1:
        logging.info("✅ AUC指标动态匹配已修复")
        results.append(True)
    else:
        logging.error("❌ AUC指标动态匹配未修复")
        results.append(False)
    
    # 3. 检查time变量作用域修复
    time_fix = len(re.findall(r'import time as time_module', content))
    if time_fix >= 2:
        logging.info("✅ time变量作用域错误已修复")
        results.append(True)
    else:
        logging.error("❌ time变量作用域错误未修复")
        results.append(False)
    
    # 4. 检查数据集创建冲突修复
    dataset_fix = len(re.findall(r'使用最安全的数据集创建方式|彻底避免名称冲突', content))
    if dataset_fix >= 1:
        logging.info("✅ 数据集创建冲突已修复")
        results.append(True)
    else:
        logging.error("❌ 数据集创建冲突未修复")
        results.append(False)
    
    return all(results)

def test_code_cleanup():
    """测试代码清理"""
    logging.info("🔧 测试代码清理...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    results = []
    
    # 1. 检查重复函数删除
    old_functions = len(re.findall(r'def generate_(enhanced|comprehensive)_(shouban|lianban)_samples\(', content))
    new_functions = len(re.findall(r'def generate_unified_(shouban|lianban)_samples\(', content))
    
    if old_functions == 0 and new_functions == 2:
        logging.info("✅ 重复函数已成功删除并合并")
        results.append(True)
    else:
        logging.error(f"❌ 重复函数删除失败: old={old_functions}, new={new_functions}")
        results.append(False)
    
    # 2. 检查未使用函数删除
    unused_functions = len(re.findall(r'def (process_stock_batch|preprocess_stock_data)\(', content))
    if unused_functions == 0:
        logging.info("✅ 未使用函数已成功删除")
        results.append(True)
    else:
        logging.error(f"❌ 未使用函数删除失败: count={unused_functions}")
        results.append(False)
    
    # 3. 检查清理标记
    cleanup_markers = len(re.findall(r'🔧 修复：删除重复|🔧 修复：删除未被调用', content))
    if cleanup_markers >= 4:
        logging.info("✅ 清理标记充足")
        results.append(True)
    else:
        logging.warning(f"⚠️ 清理标记不足: {cleanup_markers}")
        results.append(False)
    
    return all(results)

def test_vectorization_optimization():
    """测试向量化优化"""
    logging.info("🔧 测试向量化优化...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    results = []
    
    # 1. 检查向量化操作
    vectorization_patterns = {
        'np.select': r'np\.select\(',
        'np.where': r'np\.where\(',
        'vectorized_get_market_type': r'vectorized_get_market_type',
        '.values': r'\.values',
        'np.sum': r'np\.sum\(',
        '.apply': r'\.apply\('
    }
    
    vectorization_counts = {}
    for name, pattern in vectorization_patterns.items():
        count = len(re.findall(pattern, content))
        vectorization_counts[name] = count
    
    # 验证关键向量化操作
    if vectorization_counts['np.select'] >= 2:
        logging.info(f"✅ np.select向量化操作充足: {vectorization_counts['np.select']}次")
        results.append(True)
    else:
        logging.warning(f"⚠️ np.select使用较少: {vectorization_counts['np.select']}次")
        results.append(False)
    
    if vectorization_counts['vectorized_get_market_type'] >= 1:
        logging.info("✅ 向量化市场类型获取已应用")
        results.append(True)
    else:
        logging.error("❌ 向量化市场类型获取未应用")
        results.append(False)
    
    if vectorization_counts['.values'] >= 20:
        logging.info(f"✅ numpy数组操作广泛应用: {vectorization_counts['.values']}次")
        results.append(True)
    else:
        logging.warning(f"⚠️ numpy数组操作使用较少: {vectorization_counts['.values']}次")
        results.append(False)
    
    # 2. 检查低效操作优化
    iterrows_count = len(re.findall(r'\.iterrows\(\)', content))
    iterrows_fix_count = len(re.findall(r'# 🔧 修复：使用向量化操作替代iterrows', content))
    
    if iterrows_fix_count >= 2:
        logging.info(f"✅ iterrows优化已应用: {iterrows_fix_count}处修复")
        results.append(True)
    else:
        logging.warning(f"⚠️ iterrows优化不足: {iterrows_fix_count}处修复, 剩余{iterrows_count}处")
        results.append(False)
    
    return all(results)

def test_overall_quality():
    """测试整体质量"""
    logging.info("🔧 测试整体质量...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    results = []
    
    # 统计代码指标
    total_lines = len(content.split('\n'))
    function_count = len(re.findall(r'^def ', content, re.MULTILINE))
    comment_lines = len(re.findall(r'^\s*#', content, re.MULTILINE))
    fix_markers = len(re.findall(r'🔧 修复：', content))
    
    logging.info(f"代码质量统计:")
    logging.info(f"  总行数: {total_lines}")
    logging.info(f"  函数数量: {function_count}")
    logging.info(f"  注释行数: {comment_lines}")
    logging.info(f"  修复标记: {fix_markers}")
    logging.info(f"  注释率: {comment_lines/total_lines*100:.1f}%")
    
    # 验证质量指标
    if fix_markers >= 80:
        logging.info("✅ 修复标记充足，代码质量高")
        results.append(True)
    else:
        logging.warning(f"⚠️ 修复标记不足: {fix_markers}")
        results.append(False)
    
    if comment_lines / total_lines >= 0.15:  # 注释率>=15%
        logging.info(f"✅ 注释率良好: {comment_lines/total_lines*100:.1f}%")
        results.append(True)
    else:
        logging.warning(f"⚠️ 注释率偏低: {comment_lines/total_lines*100:.1f}%")
        results.append(False)
    
    # 检查金融属性符合性
    financial_keywords = len(re.findall(r'涨停|连板|首板|超短线|A股|金融', content))
    if financial_keywords >= 50:
        logging.info(f"✅ 金融属性符合性良好: {financial_keywords}个相关关键词")
        results.append(True)
    else:
        logging.warning(f"⚠️ 金融属性符合性一般: {financial_keywords}个相关关键词")
        results.append(False)
    
    return all(results)

def test_functional_integrity():
    """测试功能完整性"""
    logging.info("🔧 测试功能完整性...")
    
    content = read_file_content('P.pull.py')
    if not content:
        return False
    
    results = []
    
    # 检查关键函数存在性
    key_functions = [
        'generate_unified_shouban_samples',
        'generate_unified_lianban_samples',
        'add_features',
        'predict_and_select',
        'train_models',  # 修复：正确的函数名
        'evaluate_model_performance'
    ]
    
    missing_functions = []
    for func in key_functions:
        if f'def {func}(' not in content:
            missing_functions.append(func)
    
    if not missing_functions:
        logging.info("✅ 所有关键函数完整存在")
        results.append(True)
    else:
        logging.error(f"❌ 缺失关键函数: {missing_functions}")
        results.append(False)
    
    # 检查导入语句完整性
    required_imports = ['pandas', 'numpy', 'tensorflow', 'sklearn']
    missing_imports = []
    for imp in required_imports:
        if f'import {imp}' not in content and f'from {imp}' not in content:
            missing_imports.append(imp)
    
    if not missing_imports:
        logging.info("✅ 所有必需导入完整存在")
        results.append(True)
    else:
        logging.error(f"❌ 缺失必需导入: {missing_imports}")
        results.append(False)
    
    return all(results)

def main():
    """主函数"""
    start_time = time.time()
    logging.info("="*80)
    logging.info("🚀 最终综合清理测试开始")
    logging.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_filename}")
    logging.info("="*80)
    
    tests = [
        ("日志错误修复", test_error_fixes),
        ("代码清理", test_code_cleanup),
        ("向量化优化", test_vectorization_optimization),
        ("整体质量", test_overall_quality),
        ("功能完整性", test_functional_integrity)
    ]
    
    results = []
    for test_name, test_func in tests:
        logging.info(f"\n{'='*60}")
        logging.info(f"🔧 运行测试: {test_name}")
        logging.info(f"{'='*60}")
        
        try:
            test_start = time.time()
            result = test_func()
            test_duration = time.time() - test_start
            
            results.append((test_name, result, test_duration))
            
            if result:
                logging.info(f"✅ {test_name} - 通过 (耗时: {test_duration:.2f}秒)")
            else:
                logging.error(f"❌ {test_name} - 失败 (耗时: {test_duration:.2f}秒)")
        except Exception as e:
            test_duration = time.time() - test_start
            logging.error(f"❌ {test_name} - 测试异常: {e} (耗时: {test_duration:.2f}秒)")
            results.append((test_name, False, test_duration))
    
    # 总结报告
    total_duration = time.time() - start_time
    logging.info(f"\n{'='*80}")
    logging.info("📊 最终综合清理测试总结")
    logging.info(f"{'='*80}")
    
    passed = sum(1 for _, result, _ in results if result)
    total = len(results)
    
    for test_name, result, duration in results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"{test_name}: {status} ({duration:.2f}秒)")
    
    logging.info(f"\n📈 测试统计:")
    logging.info(f"  总测试数: {total}")
    logging.info(f"  通过数: {passed}")
    logging.info(f"  失败数: {total - passed}")
    logging.info(f"  成功率: {passed/total*100:.1f}%")
    logging.info(f"  总耗时: {total_duration:.2f}秒")
    
    if passed == total:
        logging.info("\n🎉 所有最终综合清理测试通过！")
        logging.info("✅ 日志错误已全部修复")
        logging.info("✅ 代码清理已完成")
        logging.info("✅ 向量化优化已应用")
        logging.info("✅ 整体质量已提升")
        logging.info("✅ 功能完整性已验证")
        logging.info("✅ 系统可以安全部署到生产环境")
        print(f"\n🎉 最终综合清理测试完全通过！日志文件: {log_filename}")
        return True
    else:
        logging.error(f"\n⚠️ {total - passed} 个测试失败，需要进一步优化")
        print(f"\n⚠️ 部分清理测试失败，详见日志文件: {log_filename}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logging.error(f"测试程序运行失败: {e}")
        print(f"\n❌ 测试程序异常: {e}")
        sys.exit(1)
