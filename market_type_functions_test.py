#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场类型函数测试
验证get_market_type_from_code和vectorized_get_market_type函数的：
1. 逻辑一致性
2. 功能完整性
3. 性能优化
4. 调用正确性
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
import time
from datetime import datetime

# 设置日志
log_filename = f"market_type_functions_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)

# 模拟函数定义（从P.pull.py复制）
def get_market_type_from_code(ts_code):
    """根据股票代码获取市场类型"""
    if ts_code.endswith('.BJ'):
        return 'BSE'  # 北交所
    elif ts_code.endswith('.SZ'):
        if ts_code.startswith('00'):
            return 'MAIN'  # 主板
        elif ts_code.startswith('30'):
            return 'CHINEXT'  # 创业板
    elif ts_code.endswith('.SH'):
        if ts_code.startswith('60'):
            return 'MAIN'  # 主板
        elif ts_code.startswith('688'):
            return 'STAR'  # 科创板
    return 'MAIN'  # 默认主板

def vectorized_get_market_type(ts_codes):
    """🚀 向量化获取市场类型 - 性能提升500倍"""
    if isinstance(ts_codes, str):
        return get_market_type_from_code(ts_codes)

    # 创建条件掩码
    sz_mask = ts_codes.str.endswith('.SZ')
    sh_mask = ts_codes.str.endswith('.SH')
    bj_mask = ts_codes.str.endswith('.BJ')

    # 提取代码前缀
    code_prefix = ts_codes.str[:3]

    # 🔧 修复：向量化分类，确保逻辑与get_market_type_from_code完全一致
    market_type = np.select([
        bj_mask,                                     # 北交所（优先判断）
        sz_mask & code_prefix.str.startswith('00'),  # 深圳主板
        sz_mask & code_prefix.str.startswith('30'),  # 创业板
        sh_mask & code_prefix.str.startswith('60'),  # 上海主板
        sh_mask & code_prefix.str.startswith('688'), # 科创板
    ], ['BSE', 'MAIN', 'CHINEXT', 'MAIN', 'STAR'], default='MAIN')

    return market_type

def test_logic_consistency():
    """测试两个函数的逻辑一致性"""
    logging.info("🔧 测试逻辑一致性...")
    
    # 测试用例：覆盖所有市场类型
    test_cases = [
        # 深圳主板
        ('000001.SZ', 'MAIN'),
        ('000002.SZ', 'MAIN'),
        ('002001.SZ', 'MAIN'),
        
        # 创业板
        ('300001.SZ', 'CHINEXT'),
        ('300999.SZ', 'CHINEXT'),
        
        # 上海主板
        ('600000.SH', 'MAIN'),
        ('600999.SH', 'MAIN'),
        
        # 科创板
        ('688001.SH', 'STAR'),
        ('688999.SH', 'STAR'),
        
        # 北交所
        ('430001.BJ', 'BSE'),
        ('830001.BJ', 'BSE'),
        
        # 边界情况
        ('999999.XX', 'MAIN'),  # 未知后缀
        ('', 'MAIN'),           # 空字符串
    ]
    
    results = []
    inconsistent_cases = []
    
    for ts_code, expected in test_cases:
        try:
            # 单个函数结果
            single_result = get_market_type_from_code(ts_code)
            
            # 向量化函数结果（单个输入）
            vectorized_single_result = vectorized_get_market_type(ts_code)
            
            # 向量化函数结果（Series输入）
            ts_series = pd.Series([ts_code])
            vectorized_series_result = vectorized_get_market_type(ts_series)[0]
            
            # 检查一致性
            if single_result == vectorized_single_result == vectorized_series_result == expected:
                logging.info(f"✅ {ts_code}: {single_result} (一致)")
                results.append(True)
            else:
                logging.error(f"❌ {ts_code}: single={single_result}, vec_single={vectorized_single_result}, vec_series={vectorized_series_result}, expected={expected}")
                inconsistent_cases.append((ts_code, single_result, vectorized_single_result, vectorized_series_result, expected))
                results.append(False)
                
        except Exception as e:
            logging.error(f"❌ {ts_code}: 测试异常 - {e}")
            results.append(False)
    
    if inconsistent_cases:
        logging.error(f"发现{len(inconsistent_cases)}个不一致的案例")
        for case in inconsistent_cases:
            logging.error(f"  {case}")
    
    return all(results)

def test_performance_optimization():
    """测试性能优化效果"""
    logging.info("🔧 测试性能优化...")
    
    # 生成大量测试数据
    test_codes = []
    for prefix in ['000', '002', '300', '600', '688']:
        for suffix in ['.SZ', '.SH']:
            if (prefix in ['000', '002', '300'] and suffix == '.SZ') or \
               (prefix in ['600', '688'] and suffix == '.SH'):
                for i in range(100):  # 每种类型100个
                    test_codes.append(f"{prefix}{i:03d}{suffix}")
    
    # 添加北交所
    for i in range(100):
        test_codes.append(f"430{i:03d}.BJ")
    
    ts_series = pd.Series(test_codes)
    logging.info(f"测试数据量: {len(test_codes)}个股票代码")
    
    results = []
    
    # 测试1: 列表推导式（低效方式）
    start_time = time.time()
    list_comp_results = [get_market_type_from_code(ts) for ts in test_codes]
    list_comp_time = time.time() - start_time
    
    # 测试2: 向量化函数
    start_time = time.time()
    vectorized_results = vectorized_get_market_type(ts_series).tolist()
    vectorized_time = time.time() - start_time
    
    # 验证结果一致性
    if list_comp_results == vectorized_results:
        logging.info("✅ 两种方法结果完全一致")
        results.append(True)
    else:
        logging.error("❌ 两种方法结果不一致")
        results.append(False)
    
    # 性能对比
    speedup = list_comp_time / vectorized_time if vectorized_time > 0 else float('inf')
    
    logging.info(f"性能对比:")
    logging.info(f"  列表推导式: {list_comp_time:.4f}秒")
    logging.info(f"  向量化函数: {vectorized_time:.4f}秒")
    logging.info(f"  性能提升: {speedup:.1f}倍")
    
    if speedup >= 2.0:  # 至少2倍提升
        logging.info("✅ 性能优化显著")
        results.append(True)
    else:
        logging.warning(f"⚠️ 性能提升不明显: {speedup:.1f}倍")
        results.append(False)
    
    return all(results)

def test_function_completeness():
    """测试函数完整性"""
    logging.info("🔧 测试函数完整性...")
    
    results = []
    
    # 测试所有市场类型覆盖
    all_market_types = {'MAIN', 'CHINEXT', 'STAR', 'BSE'}
    
    test_codes = [
        '000001.SZ',  # MAIN
        '300001.SZ',  # CHINEXT  
        '600001.SH',  # MAIN
        '688001.SH',  # STAR
        '430001.BJ',  # BSE
    ]
    
    # 单个函数测试
    single_results = set()
    for code in test_codes:
        result = get_market_type_from_code(code)
        single_results.add(result)
    
    # 向量化函数测试
    ts_series = pd.Series(test_codes)
    vectorized_results = set(vectorized_get_market_type(ts_series))
    
    if single_results == vectorized_results == all_market_types:
        logging.info("✅ 所有市场类型都能正确识别")
        results.append(True)
    else:
        logging.error(f"❌ 市场类型覆盖不完整:")
        logging.error(f"  期望: {all_market_types}")
        logging.error(f"  单个函数: {single_results}")
        logging.error(f"  向量化函数: {vectorized_results}")
        results.append(False)
    
    # 测试边界情况
    edge_cases = ['', '123', '999999.XX', 'invalid']
    
    for case in edge_cases:
        try:
            single_result = get_market_type_from_code(case)
            vectorized_result = vectorized_get_market_type(case)
            
            if single_result == vectorized_result == 'MAIN':
                logging.info(f"✅ 边界情况 '{case}': {single_result}")
                results.append(True)
            else:
                logging.error(f"❌ 边界情况 '{case}': single={single_result}, vectorized={vectorized_result}")
                results.append(False)
        except Exception as e:
            logging.error(f"❌ 边界情况 '{case}': 异常 - {e}")
            results.append(False)
    
    return all(results)

def test_usage_optimization():
    """测试使用优化"""
    logging.info("🔧 测试使用优化...")
    
    # 模拟P.pull.py中的使用场景
    results = []
    
    # 场景1: 批量处理（应该使用向量化函数）
    batch_codes = pd.Series(['000001.SZ', '300001.SZ', '600001.SH', '688001.SH', '430001.BJ'])
    
    try:
        # 正确的批量处理方式
        batch_results = vectorized_get_market_type(batch_codes)
        logging.info(f"✅ 批量处理成功: {len(batch_results)}个结果")
        results.append(True)
    except Exception as e:
        logging.error(f"❌ 批量处理失败: {e}")
        results.append(False)
    
    # 场景2: 单个处理（两种方式都可以）
    single_code = '000001.SZ'
    
    try:
        single_result1 = get_market_type_from_code(single_code)
        single_result2 = vectorized_get_market_type(single_code)
        
        if single_result1 == single_result2:
            logging.info(f"✅ 单个处理一致: {single_result1}")
            results.append(True)
        else:
            logging.error(f"❌ 单个处理不一致: {single_result1} vs {single_result2}")
            results.append(False)
    except Exception as e:
        logging.error(f"❌ 单个处理失败: {e}")
        results.append(False)
    
    return all(results)

def main():
    """主函数"""
    logging.info("="*80)
    logging.info("🚀 市场类型函数测试开始")
    logging.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_filename}")
    logging.info("="*80)
    
    tests = [
        ("逻辑一致性", test_logic_consistency),
        ("性能优化", test_performance_optimization),
        ("函数完整性", test_function_completeness),
        ("使用优化", test_usage_optimization)
    ]
    
    results = []
    for test_name, test_func in tests:
        logging.info(f"\n{'='*60}")
        logging.info(f"🔧 运行测试: {test_name}")
        logging.info(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logging.info(f"✅ {test_name} - 通过")
            else:
                logging.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logging.error(f"❌ {test_name} - 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结报告
    logging.info(f"\n{'='*80}")
    logging.info("📊 市场类型函数测试总结")
    logging.info(f"{'='*80}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"{test_name}: {status}")
    
    logging.info(f"\n📈 测试统计:")
    logging.info(f"  总测试数: {total}")
    logging.info(f"  通过数: {passed}")
    logging.info(f"  失败数: {total - passed}")
    logging.info(f"  成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        logging.info("\n🎉 所有市场类型函数测试通过！")
        logging.info("✅ 逻辑一致性已验证")
        logging.info("✅ 性能优化已确认")
        logging.info("✅ 函数完整性已验证")
        logging.info("✅ 使用优化已确认")
        print(f"\n🎉 市场类型函数测试完全通过！日志文件: {log_filename}")
        return True
    else:
        logging.error(f"\n⚠️ {total - passed} 个测试失败，需要进一步优化")
        print(f"\n⚠️ 部分函数测试失败，详见日志文件: {log_filename}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logging.error(f"测试程序运行失败: {e}")
        print(f"\n❌ 测试程序异常: {e}")
        sys.exit(1)
